
import { useState } from "react";
import { Sidebar } from "./Sidebar";
import { MainEditor } from "./MainEditor";
import { LiteratureSearch } from "./LiteratureSearch";
import { CitationManager } from "./CitationManager";
import { AIPaperGenerator } from "./AIPaperGenerator";
import { AIBookGenerator } from "./AIBookGenerator";

export type ActiveView = "editor" | "search" | "citations" | "chat" | "ai-generator" | "book-generator";

export function ResearchDashboard() {
  const [activeView, setActiveView] = useState<ActiveView>("editor");

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Sidebar */}
      <Sidebar activeView={activeView} onViewChange={setActiveView} />
      
      {/* Main Content */}
      <div className="flex-1">
        {activeView === "editor" && <MainEditor />}
        {activeView === "search" && <LiteratureSearch />}
        {activeView === "citations" && <CitationManager />}
        {activeView === "ai-generator" && <AIPaperGenerator />}
        {activeView === "book-generator" && <AIBookGenerator />}
        {activeView === "chat" && (
          <div className="p-8">
            <h2 className="text-2xl font-bold mb-4">AI Chat</h2>
            <p className="text-gray-600">AI Chat is now integrated into the Editor. Switch to the Editor tab to access AI writing assistance.</p>
          </div>
        )}
      </div>
    </div>
  );
}
