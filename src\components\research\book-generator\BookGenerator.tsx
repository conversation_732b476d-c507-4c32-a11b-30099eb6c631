import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  BookOpen, 
  FileText, 
  Settings, 
  Download,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Clock,
  Zap
} from "lucide-react";
import { toast } from "sonner";

import { BookState, ChapterState, BookMeta } from './types';
import { DEFAULT_BOOK_META, DEFAULT_BOOK_SETTINGS } from './constants';
import { BookMetadataForm } from './BookMetadataForm';
import { OutlinePage } from './OutlinePage';
import { GenerationPage } from './GenerationPage';
import { EditorPage } from './EditorPage';
import { ExportWizard } from './ExportWizard';
import { useBookStore } from './hooks/useBookStore';

type BookGeneratorStep = 'metadata' | 'outline' | 'generation' | 'editor' | 'export';

export function BookGenerator() {
  const {
    bookState,
    initializeBook,
    updateMetadata,
    updateChapter,
    addChapter,
    lockChapter,
    resetBook
  } = useBookStore();

  const [currentStep, setCurrentStep] = useState<BookGeneratorStep>('metadata');
  const [selectedChapterId, setSelectedChapterId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize book state if empty
  useEffect(() => {
    if (!bookState || !bookState.meta || (!bookState.meta.title && !(bookState.chapters && bookState.chapters.length))) {
      initializeBook();
    }
    setIsInitialized(true);
  }, [bookState, initializeBook]);

  // Show loading state while initializing
  if (!isInitialized || !bookState) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing Book Generator...</p>
        </div>
      </div>
    );
  }

  // Calculate overall progress
  const calculateProgress = () => {
    if (!bookState?.chapters?.length) return 0;

    const totalChapters = bookState.chapters.length;
    const completedChapters = bookState.chapters.filter(
      chapter => chapter.status === 'locked'
    ).length;

    return Math.round((completedChapters / totalChapters) * 100);
  };

  // Get step progress indicator
  const getStepStatus = (step: BookGeneratorStep) => {
    const stepOrder: BookGeneratorStep[] = ['metadata', 'outline', 'generation', 'editor', 'export'];
    const currentIndex = stepOrder.indexOf(currentStep);
    const stepIndex = stepOrder.indexOf(step);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  // Navigation handlers
  const handleStepChange = (step: BookGeneratorStep) => {
    // Validate before allowing navigation
    if (step === 'outline' && (!bookState || !bookState.meta || !bookState.meta.title)) {
      toast.error("Please complete the book metadata first");
      return;
    }
    if (step === 'generation' && (!bookState || !bookState.chapters || !bookState.chapters.length)) {
      toast.error("Please create the book outline first");
      return;
    }

    setCurrentStep(step);
  };

  const handleMetadataComplete = (metadata: BookMeta) => {
    updateMetadata(metadata);
    setCurrentStep('outline');
    toast.success("Book metadata saved successfully");
  };

  const handleOutlineComplete = () => {
    setCurrentStep('generation');
    toast.success("Book outline created successfully");
  };

  const handleChapterEdit = (chapterId: string) => {
    setSelectedChapterId(chapterId);
    setCurrentStep('editor');
  };

  const handleGenerationComplete = () => {
    const allChaptersLocked = bookState?.chapters?.every(
      chapter => chapter.status === 'locked'
    ) || false;

    if (allChaptersLocked) {
      toast.success("All chapters completed! Ready for export.");
      setCurrentStep('export');
    } else {
      toast.info("Continue editing chapters or proceed to export");
    }
  };

  const handleExportComplete = () => {
    toast.success("Book exported successfully!");
  };

  const handleReset = () => {
    if (confirm("Are you sure you want to start over? This will delete all progress.")) {
      resetBook();
      setCurrentStep('metadata');
      setSelectedChapterId(null);
      toast.info("Book generator reset");
    }
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { id: 'metadata', label: 'Metadata', icon: FileText },
      { id: 'outline', label: 'Outline', icon: BookOpen },
      { id: 'generation', label: 'Generate', icon: Zap },
      { id: 'editor', label: 'Edit', icon: Settings },
      { id: 'export', label: 'Export', icon: Download }
    ] as const;

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => {
          const status = getStepStatus(step.id);
          const Icon = step.icon;
          
          return (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <Button
                  variant={status === 'current' ? 'default' : status === 'completed' ? 'secondary' : 'outline'}
                  size="sm"
                  className={`w-12 h-12 rounded-full p-0 ${
                    status === 'completed' ? 'bg-green-100 text-green-600 border-green-300' :
                    status === 'current' ? 'bg-blue-600 text-white' :
                    'bg-gray-100 text-gray-400'
                  }`}
                  onClick={() => handleStepChange(step.id)}
                >
                  {status === 'completed' ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <Icon className="h-5 w-5" />
                  )}
                </Button>
                <span className={`text-xs mt-2 ${
                  status === 'current' ? 'text-blue-600 font-medium' :
                  status === 'completed' ? 'text-green-600' :
                  'text-gray-400'
                }`}>
                  {step.label}
                </span>
              </div>
              {index < steps.length - 1 && (
                <ArrowRight className={`h-4 w-4 ml-4 ${
                  status === 'completed' ? 'text-green-400' : 'text-gray-300'
                }`} />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Render progress summary
  const renderProgressSummary = () => {
    const progress = calculateProgress();
    const totalChapters = bookState && bookState.chapters ? bookState.chapters.length : 0;
    const completedChapters = bookState && bookState.chapters ? bookState.chapters.filter(c => c.status === 'locked').length : 0;
    
    return (
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              {(bookState && bookState.meta && bookState.meta.title) || 'Untitled Book'}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {completedChapters}/{totalChapters} Chapters
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="text-red-600 hover:text-red-700"
              >
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Overall Progress</span>
              <span className="font-medium">{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            AI Book Generator
          </h1>
          <p className="text-xl text-gray-600">
            Create comprehensive books with context-aware AI assistance
          </p>
        </div>

        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Progress Summary */}
        {(bookState && bookState.meta && bookState.meta.title) && renderProgressSummary()}

        {/* Main Content */}
        <div className="max-w-6xl mx-auto">
          {currentStep === 'metadata' && bookState && (
            <BookMetadataForm
              initialData={(bookState.meta) || {}}
              onComplete={handleMetadataComplete}
            />
          )}

          {currentStep === 'outline' && bookState && (
            <OutlinePage
              bookMeta={bookState.meta || {}}
              chapters={bookState.chapters}
              onComplete={handleOutlineComplete}
              onChapterUpdate={updateChapter}
              onChapterAdd={addChapter}
            />
          )}

          {currentStep === 'generation' && bookState && (
            <GenerationPage
              bookState={bookState}
              onChapterEdit={handleChapterEdit}
              onComplete={handleGenerationComplete}
            />
          )}

          {currentStep === 'editor' && selectedChapterId && (
            <EditorPage
              chapterId={selectedChapterId}
              onBack={() => setCurrentStep('generation')}
              onSave={(chapterId, updates) => {
                updateChapter(chapterId, updates);
                toast.success("Chapter saved successfully");
              }}
            />
          )}

          {currentStep === 'export' && bookState && (
            <ExportWizard
              bookState={bookState}
              onComplete={handleExportComplete}
              onBack={() => setCurrentStep('generation')}
            />
          )}
        </div>
      </div>
    </div>
  );
}
