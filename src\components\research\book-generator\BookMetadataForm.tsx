import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  BookOpen, 
  Users, 
  Tag, 
  FileText,
  Plus,
  X,
  Target,
  Hash
} from "lucide-react";
import { toast } from "sonner";

import { BookMeta } from './types';
import { BOOK_GENRES, VALIDATION_RULES, DEFAULT_BOOK_META } from './constants';

interface BookMetadataFormProps {
  initialData?: Partial<BookMeta>;
  onComplete: (metadata: BookMeta) => void;
}

export function BookMetadataForm({ initialData = {}, onComplete }: BookMetadataFormProps) {
  const [formData, setFormData] = useState<BookMeta>({
    ...DEFAULT_BOOK_META,
    ...initialData
  });

  const [newAuthor, setNewAuthor] = useState('');
  const [newKeyword, setNewKeyword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Title validation
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length < VALIDATION_RULES.title.minLength) {
      newErrors.title = `Title must be at least ${VALIDATION_RULES.title.minLength} characters`;
    } else if (formData.title.length > VALIDATION_RULES.title.maxLength) {
      newErrors.title = `Title must be less than ${VALIDATION_RULES.title.maxLength} characters`;
    }

    // Authors validation
    if (formData.authors.length === 0) {
      newErrors.authors = 'At least one author is required';
    } else if (formData.authors.length > VALIDATION_RULES.authors.maxItems) {
      newErrors.authors = `Maximum ${VALIDATION_RULES.authors.maxItems} authors allowed`;
    }

    // Genre validation
    if (!formData.genre) {
      newErrors.genre = 'Genre is required';
    }

    // Keywords validation
    if (formData.keywords.length > VALIDATION_RULES.keywords.maxItems) {
      newErrors.keywords = `Maximum ${VALIDATION_RULES.keywords.maxItems} keywords allowed`;
    }

    // Description validation
    if (formData.description && formData.description.length > VALIDATION_RULES.description.maxLength) {
      newErrors.description = `Description must be less than ${VALIDATION_RULES.description.maxLength} characters`;
    }

    // Estimated length validation
    if (formData.estimatedLength && (formData.estimatedLength < 10 || formData.estimatedLength > 2000)) {
      newErrors.estimatedLength = 'Estimated length must be between 10 and 2000 pages';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onComplete(formData);
      toast.success("Book metadata saved successfully!");
    } else {
      toast.error("Please fix the validation errors");
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof BookMeta, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Add author
  const addAuthor = () => {
    if (newAuthor.trim() && !formData.authors.includes(newAuthor.trim())) {
      handleInputChange('authors', [...formData.authors, newAuthor.trim()]);
      setNewAuthor('');
    }
  };

  // Remove author
  const removeAuthor = (index: number) => {
    handleInputChange('authors', formData.authors.filter((_, i) => i !== index));
  };

  // Add keyword
  const addKeyword = () => {
    if (newKeyword.trim() && !formData.keywords.includes(newKeyword.trim())) {
      handleInputChange('keywords', [...formData.keywords, newKeyword.trim()]);
      setNewKeyword('');
    }
  };

  // Remove keyword
  const removeKeyword = (index: number) => {
    handleInputChange('keywords', formData.keywords.filter((_, i) => i !== index));
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-8">
          <CardTitle className="text-3xl flex items-center justify-center gap-3">
            <BookOpen className="h-8 w-8 text-blue-600" />
            Book Metadata
          </CardTitle>
          <CardDescription className="text-lg">
            Provide essential information about your book to guide the AI generation process
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Title and Subtitle */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-base font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Title *
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter your book title"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && (
                  <p className="text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="subtitle" className="text-base font-medium">
                  Subtitle
                </Label>
                <Input
                  id="subtitle"
                  value={formData.subtitle || ''}
                  onChange={(e) => handleInputChange('subtitle', e.target.value)}
                  placeholder="Optional subtitle"
                />
              </div>
            </div>

            {/* Authors */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Authors *
              </Label>
              
              <div className="flex gap-2">
                <Input
                  value={newAuthor}
                  onChange={(e) => setNewAuthor(e.target.value)}
                  placeholder="Add author name"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAuthor())}
                />
                <Button type="button" onClick={addAuthor} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.authors.map((author, index) => (
                  <Badge key={index} variant="secondary" className="px-3 py-1">
                    {author}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-4 w-4 p-0"
                      onClick={() => removeAuthor(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              
              {errors.authors && (
                <p className="text-sm text-red-600">{errors.authors}</p>
              )}
            </div>

            {/* Genre and Target Audience */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-base font-medium flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Genre *
                </Label>
                <Select
                  value={formData.genre}
                  onValueChange={(value) => handleInputChange('genre', value)}
                >
                  <SelectTrigger className={errors.genre ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select a genre" />
                  </SelectTrigger>
                  <SelectContent>
                    {BOOK_GENRES.map((genre) => (
                      <SelectItem key={genre.id} value={genre.id}>
                        <div className="flex items-center gap-2">
                          <genre.icon className="h-4 w-4" />
                          {genre.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.genre && (
                  <p className="text-sm text-red-600">{errors.genre}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetAudience" className="text-base font-medium flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Target Audience
                </Label>
                <Input
                  id="targetAudience"
                  value={formData.targetAudience || ''}
                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                  placeholder="e.g., Graduate students, Business professionals"
                />
              </div>
            </div>

            {/* Keywords */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Keywords
              </Label>
              
              <div className="flex gap-2">
                <Input
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  placeholder="Add keyword or topic"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                />
                <Button type="button" onClick={addKeyword} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.keywords.map((keyword, index) => (
                  <Badge key={index} variant="outline" className="px-3 py-1">
                    {keyword}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-4 w-4 p-0"
                      onClick={() => removeKeyword(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              
              {errors.keywords && (
                <p className="text-sm text-red-600">{errors.keywords}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-base font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of your book's content and purpose"
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Estimated Length */}
            <div className="space-y-2">
              <Label htmlFor="estimatedLength" className="text-base font-medium">
                Estimated Length (pages)
              </Label>
              <Input
                id="estimatedLength"
                type="number"
                value={formData.estimatedLength || ''}
                onChange={(e) => handleInputChange('estimatedLength', parseInt(e.target.value) || undefined)}
                placeholder="200"
                min="10"
                max="2000"
                className={errors.estimatedLength ? 'border-red-500' : ''}
              />
              {errors.estimatedLength && (
                <p className="text-sm text-red-600">{errors.estimatedLength}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-center pt-6">
              <Button 
                type="submit" 
                size="lg"
                className="px-8 py-3 bg-blue-600 hover:bg-blue-700"
              >
                Continue to Outline
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
