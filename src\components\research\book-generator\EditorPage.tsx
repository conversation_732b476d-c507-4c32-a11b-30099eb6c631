import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  ArrowLeft, 
  Save, 
  RefreshCw, 
  Eye, 
  Edit,
  FileText,
  Hash,
  Clock,
  BookOpen
} from "lucide-react";
import { toast } from "sonner";

import { ChapterState } from './types';
import { useBookStore } from './hooks/useBookStore';
import { SummaryBadge } from './components/SummaryBadge';
import summaryService from './services/summary.service';

interface EditorPageProps {
  chapterId: string;
  onBack: () => void;
  onSave: (chapterId: string, updates: Partial<ChapterState>) => void;
}

export function EditorPage({ chapterId, onBack, onSave }: EditorPageProps) {
  const { getChapterById } = useBookStore();
  const chapter = getChapterById(chapterId);

  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState('');
  const [editedContent, setEditedContent] = useState('');
  const [editedSummary, setEditedSummary] = useState('');
  const [isRegeneratingSummary, setIsRegeneratingSummary] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Initialize editor state
  useEffect(() => {
    if (chapter) {
      setEditedTitle(chapter.title);
      setEditedContent(chapter.draft);
      setEditedSummary(chapter.summary || '');
    }
  }, [chapter]);

  // Track unsaved changes
  useEffect(() => {
    if (chapter) {
      const hasChanges = 
        editedTitle !== chapter.title ||
        editedContent !== chapter.draft ||
        editedSummary !== (chapter.summary || '');
      setHasUnsavedChanges(hasChanges);
    }
  }, [editedTitle, editedContent, editedSummary, chapter]);

  if (!chapter) {
    return (
      <div className="max-w-6xl mx-auto p-8">
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Chapter not found</h3>
            <Button onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate reading stats
  const wordCount = editedContent.split(/\s+/).filter(word => word.length > 0).length;
  const estimatedReadTime = Math.ceil(wordCount / 200); // 200 words per minute

  // Save changes
  const handleSave = () => {
    const updates: Partial<ChapterState> = {
      title: editedTitle,
      draft: editedContent,
      summary: editedSummary || null,
      wordCount,
      estimatedReadTime
    };

    onSave(chapterId, updates);
    setHasUnsavedChanges(false);
    toast.success("Chapter saved successfully!");
  };

  // Regenerate summary
  const handleRegenerateSummary = async () => {
    if (!editedContent.trim()) {
      toast.error("Cannot generate summary for empty content");
      return;
    }

    setIsRegeneratingSummary(true);
    
    try {
      const summaryResponse = await summaryService.generateChapterSummary(
        editedContent,
        editedTitle,
        chapter.chapterNumber
      );

      if (summaryResponse.success) {
        setEditedSummary(summaryResponse.summary);
        toast.success("Summary regenerated successfully!");
      } else {
        toast.error("Failed to regenerate summary");
      }
    } catch (error) {
      toast.error("Error regenerating summary");
    } finally {
      setIsRegeneratingSummary(false);
    }
  };

  // Toggle edit mode
  const toggleEditMode = () => {
    if (isEditing && hasUnsavedChanges) {
      if (confirm("You have unsaved changes. Do you want to save them?")) {
        handleSave();
      }
    }
    setIsEditing(!isEditing);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Generation
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Chapter {chapter.chapterNumber}: {chapter.title}
              </h1>
              <div className="flex items-center gap-4 mt-1">
                <Badge variant="outline" className="flex items-center gap-1">
                  <Hash className="h-3 w-3" />
                  {wordCount.toLocaleString()} words
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {estimatedReadTime} min read
                </Badge>
                <Badge 
                  variant={chapter.status === 'locked' ? 'default' : 'secondary'}
                  className="flex items-center gap-1"
                >
                  <BookOpen className="h-3 w-3" />
                  {chapter.status}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={toggleEditMode}
              className="flex items-center gap-2"
            >
              {isEditing ? <Eye className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
              {isEditing ? 'Preview' : 'Edit'}
            </Button>
            {hasUnsavedChanges && (
              <Button
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                Save Changes
              </Button>
            )}
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Chapter Content
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="title">Chapter Title</Label>
                      <Input
                        id="title"
                        value={editedTitle}
                        onChange={(e) => setEditedTitle(e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        value={editedContent}
                        onChange={(e) => setEditedContent(e.target.value)}
                        rows={25}
                        className="mt-1 font-mono text-sm"
                        placeholder="Write your chapter content in markdown..."
                      />
                    </div>
                  </div>
                ) : (
                  <ScrollArea className="h-[600px] pr-4">
                    <div className="prose prose-sm max-w-none">
                      <h2 className="text-xl font-bold mb-4">{editedTitle}</h2>
                      <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                        {editedContent || (
                          <div className="text-center py-12 text-gray-500">
                            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No content yet. Switch to edit mode to add content.</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Summary */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Chapter Summary</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRegenerateSummary}
                    disabled={isRegeneratingSummary || !editedContent.trim()}
                    className="flex items-center gap-1"
                  >
                    <RefreshCw className={`h-3 w-3 ${isRegeneratingSummary ? 'animate-spin' : ''}`} />
                    Regenerate
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <Textarea
                    value={editedSummary}
                    onChange={(e) => setEditedSummary(e.target.value)}
                    rows={8}
                    placeholder="Chapter summary..."
                    className="text-sm"
                  />
                ) : (
                  <div className="text-sm text-gray-700">
                    {editedSummary || (
                      <div className="text-center py-8 text-gray-500">
                        <p>No summary available</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Chapter Stats */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Chapter Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Word Count</span>
                    <span className="text-sm font-medium">{wordCount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Reading Time</span>
                    <span className="text-sm font-medium">{estimatedReadTime} minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Status</span>
                    <Badge variant="outline" className="text-xs">
                      {chapter.status}
                    </Badge>
                  </div>
                  {chapter.glossaryTerms && chapter.glossaryTerms.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Glossary Terms</span>
                      <span className="text-sm font-medium">{chapter.glossaryTerms.length}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Glossary Terms */}
            {chapter.glossaryTerms && chapter.glossaryTerms.length > 0 && (
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-lg">Glossary Terms</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {chapter.glossaryTerms.map((term, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {term}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
