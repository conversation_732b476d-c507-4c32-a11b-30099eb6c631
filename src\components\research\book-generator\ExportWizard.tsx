import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Download, 
  FileText, 
  BookOpen, 
  Settings,
  ArrowLeft,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

import { BookState, ExportOptions } from './types';
import { EXPORT_FORMATS, CITATION_STYLES } from './constants';
import exportBookService from './services/export-book.service';

interface ExportWizardProps {
  bookState: BookState;
  onComplete: () => void;
  onBack: () => void;
}

export function ExportWizard({ bookState, onComplete, onBack }: ExportWizardProps) {
  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeTableOfContents: true,
    includeFigureList: true,
    includeReferences: true,
    includeIndex: false,
    includeGlossary: true,
    chapterBreaks: true,
    pageNumbers: true,
    headerFooter: true,
    customStyles: ''
  });

  // Calculate export statistics
  const stats = {
    totalChapters: bookState.chapters.length,
    completedChapters: bookState.chapters.filter(c => c.status === 'locked').length,
    totalWords: bookState.chapters.reduce((sum, c) => sum + (c.wordCount || 0), 0),
    totalCitations: Object.keys(bookState.citationGraph).length,
    totalAppendices: bookState.appendices.length,
    estimatedPages: Math.ceil(bookState.chapters.reduce((sum, c) => sum + (c.wordCount || 0), 0) / 250) // 250 words per page
  };

  const selectedFormatConfig = EXPORT_FORMATS.find(f => f.id === selectedFormat);

  // Handle export option changes
  const updateExportOption = <K extends keyof ExportOptions>(
    key: K, 
    value: ExportOptions[K]
  ) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  // Handle export
  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      const options: ExportOptions = {
        ...exportOptions,
        format: selectedFormat as ExportOptions['format']
      };

      switch (selectedFormat) {
        case 'pdf':
          await exportBookService.exportToPDF(bookState, options);
          break;
        case 'epub':
          await exportBookService.exportToEPUB(bookState, options);
          break;
        case 'latex':
          await exportBookService.exportToLaTeX(bookState, options);
          break;
        case 'docx':
          await exportBookService.exportToDocx(bookState, options);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      toast.success(`Book exported successfully as ${selectedFormat.toUpperCase()}!`);
      onComplete();
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl flex items-center justify-center gap-3">
            <Download className="h-8 w-8 text-blue-600" />
            Export Your Book
          </CardTitle>
          <CardDescription className="text-lg">
            Choose your export format and customize the output
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Export Statistics */}
        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Book Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Title</span>
                <span className="text-sm font-medium truncate ml-2">
                  {bookState.meta.title}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Chapters</span>
                <Badge variant="outline">
                  {stats.completedChapters}/{stats.totalChapters}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Words</span>
                <Badge variant="outline">
                  {stats.totalWords.toLocaleString()}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Est. Pages</span>
                <Badge variant="outline">
                  ~{stats.estimatedPages}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Citations</span>
                <Badge variant="outline">
                  {stats.totalCitations}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Appendices</span>
                <Badge variant="outline">
                  {stats.totalAppendices}
                </Badge>
              </div>
            </div>

            {stats.completedChapters < stats.totalChapters && (
              <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> {stats.totalChapters - stats.completedChapters} chapters 
                  are not yet completed. They will be included with their current content.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Export Options */}
        <div className="lg:col-span-2 space-y-6">
          {/* Format Selection */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-lg">Export Format</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {EXPORT_FORMATS.map(format => (
                  <div
                    key={format.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedFormat === format.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedFormat(format.id)}
                  >
                    <div className="flex items-start gap-3">
                      <format.icon className={`h-6 w-6 mt-1 ${
                        selectedFormat === format.id ? 'text-blue-600' : 'text-gray-600'
                      }`} />
                      <div className="flex-1">
                        <h3 className={`font-medium ${
                          selectedFormat === format.id ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {format.name}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {format.description}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {format.features.map(feature => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Export Settings */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Export Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Content Options */}
                <div>
                  <h4 className="font-medium mb-3">Content Options</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="toc"
                        checked={exportOptions.includeTableOfContents}
                        onCheckedChange={(checked) => 
                          updateExportOption('includeTableOfContents', !!checked)
                        }
                      />
                      <Label htmlFor="toc">Table of Contents</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="figures"
                        checked={exportOptions.includeFigureList}
                        onCheckedChange={(checked) => 
                          updateExportOption('includeFigureList', !!checked)
                        }
                      />
                      <Label htmlFor="figures">List of Figures</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="references"
                        checked={exportOptions.includeReferences}
                        onCheckedChange={(checked) => 
                          updateExportOption('includeReferences', !!checked)
                        }
                      />
                      <Label htmlFor="references">References</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="glossary"
                        checked={exportOptions.includeGlossary}
                        onCheckedChange={(checked) => 
                          updateExportOption('includeGlossary', !!checked)
                        }
                      />
                      <Label htmlFor="glossary">Glossary</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="index"
                        checked={exportOptions.includeIndex}
                        onCheckedChange={(checked) => 
                          updateExportOption('includeIndex', !!checked)
                        }
                      />
                      <Label htmlFor="index">Index</Label>
                    </div>
                  </div>
                </div>

                {/* Formatting Options */}
                <div>
                  <h4 className="font-medium mb-3">Formatting Options</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="chapter-breaks"
                        checked={exportOptions.chapterBreaks}
                        onCheckedChange={(checked) => 
                          updateExportOption('chapterBreaks', !!checked)
                        }
                      />
                      <Label htmlFor="chapter-breaks">Chapter Page Breaks</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="page-numbers"
                        checked={exportOptions.pageNumbers}
                        onCheckedChange={(checked) => 
                          updateExportOption('pageNumbers', !!checked)
                        }
                      />
                      <Label htmlFor="page-numbers">Page Numbers</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="header-footer"
                        checked={exportOptions.headerFooter}
                        onCheckedChange={(checked) => 
                          updateExportOption('headerFooter', !!checked)
                        }
                      />
                      <Label htmlFor="header-footer">Headers & Footers</Label>
                    </div>
                  </div>
                </div>

                {/* Custom Styles */}
                <div>
                  <Label htmlFor="custom-styles">Custom Styles (CSS/LaTeX)</Label>
                  <Textarea
                    id="custom-styles"
                    value={exportOptions.customStyles}
                    onChange={(e) => updateExportOption('customStyles', e.target.value)}
                    placeholder="Add custom styling rules..."
                    rows={4}
                    className="mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Generation
        </Button>
        
        <Button
          onClick={handleExport}
          disabled={isExporting}
          className="bg-green-600 hover:bg-green-700 flex items-center gap-2 px-8"
        >
          {isExporting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="h-4 w-4" />
              Export as {selectedFormatConfig?.name}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
