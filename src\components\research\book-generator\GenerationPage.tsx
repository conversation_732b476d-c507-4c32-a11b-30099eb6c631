import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Zap, 
  BookOpen, 
  Settings, 
  Play, 
  Pause,
  CheckCircle,
  ArrowRight,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";

import { BookState, ChapterState, GenerationQueueItem, ChapterGenerationRequest } from './types';
import { ChapterQueuePanel } from './components/ChapterQueuePanel';
import { useContextPack } from './hooks/useContextPack';
import { useBookStore } from './hooks/useBookStore';
import bookAIService from './services/book-ai.service';
import summaryService from './services/summary.service';

interface GenerationPageProps {
  bookState: BookState;
  onChapterEdit: (chapterId: string) => void;
  onComplete: () => void;
}

export function GenerationPage({ bookState, onChapterEdit, onComplete }: GenerationPageProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentGeneratingChapter, setCurrentGeneratingChapter] = useState<string | null>(null);
  const [generationQueue, setGenerationQueue] = useState<GenerationQueueItem[]>([]);
  const [generationProgress, setGenerationProgress] = useState(0);

  const { 
    updateChapter, 
    saveDraft, 
    saveSummary, 
    lockChapter, 
    unlockChapter,
    updateChapterStatus 
  } = useBookStore();
  
  const { buildContext, validateContext, getFormattedContext } = useContextPack();

  // Initialize generation queue
  useEffect(() => {
    const queue: GenerationQueueItem[] = bookState.chapters.map(chapter => ({
      chapterId: chapter.id,
      chapterNumber: chapter.chapterNumber,
      title: chapter.title,
      status: chapter.status === 'pending' ? 'pending' : 'completed',
      progress: chapter.status === 'locked' ? 100 : 0
    }));
    
    setGenerationQueue(queue);
  }, [bookState.chapters]);

  // Calculate overall progress
  const calculateOverallProgress = () => {
    const totalChapters = bookState.chapters.length;
    const completedChapters = bookState.chapters.filter(
      chapter => chapter.status === 'locked'
    ).length;
    
    return totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0;
  };

  // Update queue item
  const updateQueueItem = (chapterId: string, updates: Partial<GenerationQueueItem>) => {
    setGenerationQueue(prev => 
      prev.map(item => 
        item.chapterId === chapterId ? { ...item, ...updates } : item
      )
    );
  };

  // Generate a single chapter
  const generateChapter = async (chapter: ChapterState): Promise<boolean> => {
    try {
      setCurrentGeneratingChapter(chapter.id);
      updateQueueItem(chapter.id, { status: 'generating', progress: 0 });
      updateChapterStatus(chapter.id, 'generating');

      // Build context for this chapter
      const chapterIndex = bookState.chapters.findIndex(c => c.id === chapter.id);
      const contextValidation = validateContext(chapterIndex);
      
      if (!contextValidation.isValid) {
        throw new Error(`Context validation failed: ${contextValidation.issues.join(', ')}`);
      }

      const contextPack = buildContext(chapterIndex);
      
      // Prepare generation request
      const request: ChapterGenerationRequest = {
        chapterTitle: chapter.title,
        chapterNumber: chapter.chapterNumber,
        outline: chapter.outline,
        contextPack,
        generationSettings: bookState.generationSettings
      };

      // Update progress
      updateQueueItem(chapter.id, { progress: 20 });

      // Generate chapter content
      const generationResponse = await bookAIService.generateChapter(request);
      
      if (!generationResponse.success) {
        throw new Error(generationResponse.error || 'Chapter generation failed');
      }

      // Update progress
      updateQueueItem(chapter.id, { progress: 60 });

      // Save the draft
      saveDraft(chapter.id, generationResponse.content);

      // Update progress
      updateQueueItem(chapter.id, { progress: 80 });

      // Generate summary
      const summaryResponse = await summaryService.generateChapterSummary(
        generationResponse.content,
        chapter.title,
        chapter.chapterNumber
      );

      if (summaryResponse.success) {
        saveSummary(chapter.id, summaryResponse.summary, summaryResponse.glossaryTerms);
      }

      // Complete
      updateQueueItem(chapter.id, { status: 'completed', progress: 100 });
      
      toast.success(`Chapter ${chapter.chapterNumber} generated successfully!`);
      return true;

    } catch (error) {
      console.error(`Failed to generate chapter ${chapter.chapterNumber}:`, error);
      updateQueueItem(chapter.id, { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      updateChapterStatus(chapter.id, 'pending');
      toast.error(`Failed to generate Chapter ${chapter.chapterNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      setCurrentGeneratingChapter(null);
    }
  };

  // Start generation process
  const startGeneration = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);

    const pendingChapters = bookState.chapters.filter(
      chapter => chapter.status === 'pending'
    );

    if (pendingChapters.length === 0) {
      toast.info("No pending chapters to generate");
      setIsGenerating(false);
      return;
    }

    let successCount = 0;
    
    for (let i = 0; i < pendingChapters.length; i++) {
      const chapter = pendingChapters[i];
      
      // Check if generation should continue
      if (!isGenerating) {
        break;
      }

      const success = await generateChapter(chapter);
      if (success) {
        successCount++;
      }

      // Update overall progress
      const progress = Math.round(((i + 1) / pendingChapters.length) * 100);
      setGenerationProgress(progress);

      // Small delay between chapters
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    setIsGenerating(false);
    setGenerationProgress(100);

    if (successCount === pendingChapters.length) {
      toast.success(`All ${successCount} chapters generated successfully!`);
      onComplete();
    } else {
      toast.warning(`Generated ${successCount} of ${pendingChapters.length} chapters. Some failed.`);
    }
  };

  // Pause generation
  const pauseGeneration = () => {
    setIsGenerating(false);
    toast.info("Generation paused");
  };

  // Regenerate a specific chapter
  const regenerateChapter = async (chapterId: string) => {
    const chapter = bookState.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    await generateChapter(chapter);
  };

  // Handle chapter locking
  const handleLockChapter = (chapterId: string) => {
    lockChapter(chapterId);
    updateQueueItem(chapterId, { status: 'completed', progress: 100 });
    toast.success("Chapter locked successfully");
  };

  // Handle chapter unlocking
  const handleUnlockChapter = (chapterId: string) => {
    unlockChapter(chapterId);
    toast.info("Chapter unlocked for editing");
  };

  const overallProgress = calculateOverallProgress();
  const allChaptersCompleted = bookState.chapters.every(c => c.status === 'locked');

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl flex items-center justify-center gap-3">
            <Zap className="h-8 w-8 text-blue-600" />
            Chapter Generation
          </CardTitle>
          <CardDescription className="text-lg">
            AI-powered chapter generation with context awareness
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Progress Overview */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Generation Progress</span>
            <Badge variant="outline">
              {bookState.chapters.filter(c => c.status === 'locked').length} / {bookState.chapters.length} Complete
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span>Overall Progress</span>
              <span className="font-medium">{overallProgress}%</span>
            </div>
            <Progress value={overallProgress} className="h-3" />
            
            {isGenerating && (
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <Zap className="h-4 w-4 animate-pulse" />
                <span>Generating chapters... This may take several minutes per chapter.</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Chapter Queue Panel */}
      <ChapterQueuePanel
        chapters={bookState.chapters}
        queue={generationQueue}
        isGenerating={isGenerating}
        onStartGeneration={startGeneration}
        onPauseGeneration={pauseGeneration}
        onRegenerateChapter={regenerateChapter}
        onEditChapter={onChapterEdit}
        onLockChapter={handleLockChapter}
        onUnlockChapter={handleUnlockChapter}
      />

      {/* Action Buttons */}
      <div className="flex justify-center gap-4">
        {allChaptersCompleted ? (
          <Button 
            size="lg" 
            onClick={onComplete}
            className="px-8 py-3 bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Proceed to Export
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <Button 
            size="lg" 
            onClick={onComplete}
            variant="outline"
            className="px-8 py-3"
          >
            Continue to Export
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  );
}
