import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  BookOpen, 
  Zap, 
  FileText, 
  Target,
  Lightbulb,
  ArrowRight,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";

import { BookMeta, ChapterState, OutlineNode } from './types';
import { OUTLINE_TEMPLATES, BOOK_GENRES } from './constants';
import { OutlineTree } from './components/OutlineTree';
import { useBookStore } from './hooks/useBookStore';

interface OutlinePageProps {
  bookMeta: BookMeta;
  chapters: ChapterState[];
  onComplete: () => void;
  onChapterUpdate: (chapterId: string, updates: Partial<ChapterState>) => void;
  onChapterAdd: (title: string, outline?: OutlineNode[]) => string;
}

export function OutlinePage({ 
  bookMeta, 
  chapters, 
  onComplete, 
  onChapterUpdate, 
  onChapterAdd 
}: OutlinePageProps) {
  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [outline, setOutline] = useState<OutlineNode[]>([]);

  // Initialize outline from existing chapters
  useEffect(() => {
    if (chapters.length > 0) {
      const chapterOutlines = chapters.map(chapter => ({
        id: chapter.id,
        title: chapter.title,
        level: 1,
        numbering: chapter.chapterNumber.toString(),
        description: `Chapter ${chapter.chapterNumber}`,
        estimatedWords: chapter.wordCount || 2000,
        children: chapter.outline || []
      }));
      setOutline(chapterOutlines);
    }
  }, [chapters]);

  // Generate ID for new outline nodes
  const generateId = () => Math.random().toString(36).substr(2, 9);

  // Generate numbering for outline nodes
  const generateNumbering = (nodes: OutlineNode[], parentNumbering = ''): OutlineNode[] => {
    return nodes.map((node, index) => {
      const numbering = parentNumbering 
        ? `${parentNumbering}.${index + 1}` 
        : (index + 1).toString();
      
      return {
        ...node,
        numbering,
        children: node.children ? generateNumbering(node.children, numbering) : undefined
      };
    });
  };

  // Apply template to create initial outline
  const applyTemplate = (templateKey: string) => {
    const template = OUTLINE_TEMPLATES[templateKey as keyof typeof OUTLINE_TEMPLATES];
    if (!template) return;

    const templateOutline: OutlineNode[] = template.map((item, index) => ({
      id: generateId(),
      title: item.title,
      level: item.level,
      numbering: (index + 1).toString(),
      estimatedWords: item.estimatedWords,
      children: []
    }));

    setOutline(generateNumbering(templateOutline));
    toast.success(`Applied ${templateKey} template`);
  };

  // Generate AI outline
  const generateAIOutline = async () => {
    setIsGeneratingOutline(true);
    
    try {
      // This would call the AI service to generate an outline
      // For now, we'll create a sample outline
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      const aiOutline: OutlineNode[] = [
        {
          id: generateId(),
          title: "Introduction and Overview",
          level: 1,
          numbering: "1",
          description: "Setting the foundation and context",
          estimatedWords: 1500,
          children: [
            {
              id: generateId(),
              title: "Background and Motivation",
              level: 2,
              numbering: "1.1",
              estimatedWords: 500,
            },
            {
              id: generateId(),
              title: "Scope and Objectives",
              level: 2,
              numbering: "1.2",
              estimatedWords: 400,
            }
          ]
        },
        {
          id: generateId(),
          title: "Core Concepts and Framework",
          level: 1,
          numbering: "2",
          description: "Fundamental principles and methodology",
          estimatedWords: 2500,
          children: []
        },
        {
          id: generateId(),
          title: "Implementation and Applications",
          level: 1,
          numbering: "3",
          description: "Practical applications and case studies",
          estimatedWords: 2000,
          children: []
        },
        {
          id: generateId(),
          title: "Conclusion and Future Directions",
          level: 1,
          numbering: "4",
          description: "Summary and recommendations",
          estimatedWords: 1200,
          children: []
        }
      ];

      setOutline(generateNumbering(aiOutline));
      toast.success("AI outline generated successfully!");
    } catch (error) {
      toast.error("Failed to generate AI outline");
    } finally {
      setIsGeneratingOutline(false);
    }
  };

  // Handle outline node operations
  const handleNodeEdit = (nodeId: string, updates: Partial<OutlineNode>) => {
    const updateNode = (nodes: OutlineNode[]): OutlineNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, ...updates };
        }
        if (node.children) {
          return { ...node, children: updateNode(node.children) };
        }
        return node;
      });
    };

    setOutline(updateNode(outline));
  };

  const handleNodeAdd = (parentId: string | null, level: number) => {
    const newNode: OutlineNode = {
      id: generateId(),
      title: level === 1 ? "New Chapter" : "New Section",
      level,
      numbering: "",
      estimatedWords: level === 1 ? 2000 : 500,
      children: []
    };

    if (parentId === null) {
      // Add as root node (chapter)
      setOutline(prev => generateNumbering([...prev, newNode]));
    } else {
      // Add as child node
      const addToParent = (nodes: OutlineNode[]): OutlineNode[] => {
        return nodes.map(node => {
          if (node.id === parentId) {
            const children = node.children || [];
            return { 
              ...node, 
              children: generateNumbering([...children, newNode], node.numbering)
            };
          }
          if (node.children) {
            return { ...node, children: addToParent(node.children) };
          }
          return node;
        });
      };

      setOutline(addToParent(outline));
    }
  };

  const handleNodeDelete = (nodeId: string) => {
    const deleteNode = (nodes: OutlineNode[]): OutlineNode[] => {
      return nodes
        .filter(node => node.id !== nodeId)
        .map(node => ({
          ...node,
          children: node.children ? deleteNode(node.children) : undefined
        }));
    };

    setOutline(generateNumbering(deleteNode(outline)));
  };

  // Save outline and proceed to generation
  const handleSaveAndContinue = () => {
    if (outline.length === 0) {
      toast.error("Please create at least one chapter");
      return;
    }

    // Convert outline to chapters
    outline.forEach((chapterNode, index) => {
      const existingChapter = chapters.find(c => c.chapterNumber === index + 1);
      
      if (existingChapter) {
        // Update existing chapter
        onChapterUpdate(existingChapter.id, {
          title: chapterNode.title,
          outline: chapterNode.children || []
        });
      } else {
        // Create new chapter
        onChapterAdd(chapterNode.title, chapterNode.children || []);
      }
    });

    onComplete();
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl flex items-center justify-center gap-3">
            <BookOpen className="h-8 w-8 text-blue-600" />
            Book Outline
          </CardTitle>
          <CardDescription className="text-lg">
            Create a detailed structure for "{bookMeta.title}"
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Quick Start Options */}
      <div className="grid md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Target className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Use Template</h3>
            <p className="text-sm text-gray-600 mb-4">Start with a pre-built structure</p>
            <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
              <SelectTrigger>
                <SelectValue placeholder="Choose template" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(OUTLINE_TEMPLATES).map(([key, template]) => (
                  <SelectItem key={key} value={key}>
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedTemplate && (
              <Button 
                className="w-full mt-3" 
                size="sm"
                onClick={() => applyTemplate(selectedTemplate)}
              >
                Apply Template
              </Button>
            )}
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Zap className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">AI Generate</h3>
            <p className="text-sm text-gray-600 mb-4">Let AI create an outline for you</p>
            <Button 
              className="w-full" 
              onClick={generateAIOutline}
              disabled={isGeneratingOutline}
            >
              {isGeneratingOutline ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Generate Outline
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <FileText className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Start Fresh</h3>
            <p className="text-sm text-gray-600 mb-4">Build your outline from scratch</p>
            <Button 
              className="w-full" 
              variant="outline"
              onClick={() => handleNodeAdd(null, 1)}
            >
              Add First Chapter
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Outline Editor */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Book Structure</span>
            <Badge variant="outline">
              {outline.length} chapters
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <OutlineTree
            outline={outline}
            onNodeEdit={handleNodeEdit}
            onNodeAdd={handleNodeAdd}
            onNodeDelete={handleNodeDelete}
          />
        </CardContent>
      </Card>

      {/* Continue Button */}
      {outline.length > 0 && (
        <div className="flex justify-center">
          <Button 
            size="lg" 
            onClick={handleSaveAndContinue}
            className="px-8 py-3 bg-blue-600 hover:bg-blue-700"
          >
            Continue to Generation
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
}
