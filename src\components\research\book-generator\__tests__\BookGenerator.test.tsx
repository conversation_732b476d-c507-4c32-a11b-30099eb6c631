import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BookGenerator } from '../BookGenerator';

// Mock the Zustand store
vi.mock('../hooks/useBookStore', () => ({
  useBookStore: () => ({
    bookState: {
      meta: {
        title: '',
        subtitle: '',
        authors: [],
        genre: 'professional',
        keywords: [],
        description: '',
        targetAudience: '',
        estimatedLength: 200
      },
      chapters: [],
      appendices: [],
      citationGraph: {},
      globalSettings: {
        citationStyle: 'apa',
        referencesPerChapter: false,
        includeIndex: true,
        includeGlossary: true,
      },
      generationSettings: {
        selectedModel: "google/gemini-2.5-flash-lite-preview-06-17",
        maxWordsPerChapter: 6000,
        tone: 'professional',
      },
    },
    initializeBook: vi.fn(),
    updateMetadata: vi.fn(),
    updateChapter: vi.fn(),
    addChapter: vi.fn(),
    lockChapter: vi.fn(),
    resetBook: vi.fn(),
  })
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  }
}));

describe('BookGenerator', () => {
  it('renders without crashing', () => {
    render(<BookGenerator />);
    expect(screen.getByText('AI Book Generator')).toBeInTheDocument();
  });

  it('shows the step indicator', () => {
    render(<BookGenerator />);
    expect(screen.getByText('Metadata')).toBeInTheDocument();
    expect(screen.getByText('Outline')).toBeInTheDocument();
    expect(screen.getByText('Generate')).toBeInTheDocument();
    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('Export')).toBeInTheDocument();
  });

  it('starts with metadata step', () => {
    render(<BookGenerator />);
    expect(screen.getByText('Book Metadata')).toBeInTheDocument();
  });
});
