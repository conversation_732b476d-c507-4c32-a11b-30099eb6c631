import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  X,
  FileText,
  BookOpen,
  List,
  Hash
} from "lucide-react";
import { toast } from "sonner";

import { Appendix } from '../types';
import { useBookStore } from '../hooks/useBookStore';

interface AppendixManagerProps {
  appendices: Appendix[];
}

export function AppendixManager({ appendices }: AppendixManagerProps) {
  const { addAppendix, updateAppendix, deleteAppendix, reorderAppendices } = useBookStore();
  
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<Appendix>>({
    title: '',
    content: '',
    type: 'custom',
    order: appendices.length
  });

  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      type: 'custom',
      order: appendices.length
    });
    setIsAdding(false);
    setEditingId(null);
  };

  const handleSave = () => {
    if (!formData.title?.trim()) {
      toast.error("Please enter a title");
      return;
    }

    if (editingId) {
      updateAppendix(editingId, formData);
      toast.success("Appendix updated successfully");
    } else {
      addAppendix(formData as Omit<Appendix, 'id'>);
      toast.success("Appendix added successfully");
    }

    resetForm();
  };

  const handleEdit = (appendix: Appendix) => {
    setFormData(appendix);
    setEditingId(appendix.id);
    setIsAdding(true);
  };

  const handleDelete = (id: string) => {
    if (confirm("Are you sure you want to delete this appendix?")) {
      deleteAppendix(id);
      toast.success("Appendix deleted");
    }
  };

  const sortedAppendices = [...appendices].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Appendix Manager
            </CardTitle>
            <Button
              onClick={() => setIsAdding(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Appendix
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Appendix List */}
      <Card>
        <CardContent className="p-6">
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {sortedAppendices.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No appendices yet</h3>
                  <p className="text-gray-600 mb-4">
                    Add appendices like glossaries, references, or additional materials
                  </p>
                  <Button onClick={() => setIsAdding(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Appendix
                  </Button>
                </div>
              ) : (
                sortedAppendices.map(appendix => (
                  <div
                    key={appendix.id}
                    className="p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs capitalize">
                            {appendix.type}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            Order: {appendix.order + 1}
                          </Badge>
                        </div>
                        
                        <h4 className="font-medium text-gray-900 mb-1">
                          {appendix.title}
                        </h4>
                        
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {appendix.content.substring(0, 150)}
                          {appendix.content.length > 150 && '...'}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(appendix)}
                          className="p-1 h-6 w-6"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(appendix.id)}
                          className="p-1 h-6 w-6 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      {isAdding && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] bg-white shadow-xl">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  {editingId ? 'Edit Appendix' : 'Add New Appendix'}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetForm}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Title *</Label>
                    <Input
                      id="title"
                      value={formData.title || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        title: e.target.value
                      }))}
                      placeholder="Appendix title"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="type">Type</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        type: value as Appendix['type']
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="glossary">Glossary</SelectItem>
                        <SelectItem value="references">References</SelectItem>
                        <SelectItem value="index">Index</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    value={formData.content || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      content: e.target.value
                    }))}
                    placeholder="Appendix content in markdown format..."
                    rows={12}
                    className="font-mono text-sm"
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
                <Button variant="outline" onClick={resetForm}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  {editingId ? 'Update' : 'Add'} Appendix
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
