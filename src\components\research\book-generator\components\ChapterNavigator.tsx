import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  ChevronLeft, 
  ChevronRight, 
  BookOpen, 
  Edit, 
  Eye,
  Lock,
  CheckCircle,
  Clock,
  Zap
} from "lucide-react";

import { ChapterState } from '../types';
import { CHAPTER_STATUS_CONFIG } from '../constants';
import { SummaryBadge } from './SummaryBadge';

interface ChapterNavigatorProps {
  chapters: ChapterState[];
  currentChapterId: string | null;
  onChapterSelect: (chapterId: string) => void;
  onChapterEdit: (chapterId: string) => void;
  showActions?: boolean;
  compact?: boolean;
}

export function ChapterNavigator({
  chapters,
  currentChapterId,
  onChapterSelect,
  onChapterEdit,
  showActions = true,
  compact = false
}: ChapterNavigatorProps) {
  
  const currentIndex = chapters.findIndex(c => c.id === currentChapterId);
  const currentChapter = currentIndex >= 0 ? chapters[currentIndex] : null;

  const handlePrevious = () => {
    if (currentIndex > 0) {
      onChapterSelect(chapters[currentIndex - 1].id);
    }
  };

  const handleNext = () => {
    if (currentIndex < chapters.length - 1) {
      onChapterSelect(chapters[currentIndex + 1].id);
    }
  };

  const renderChapterItem = (chapter: ChapterState, isActive: boolean) => {
    const statusConfig = CHAPTER_STATUS_CONFIG[chapter.status];
    const StatusIcon = statusConfig.icon;

    return (
      <div
        key={chapter.id}
        className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
          isActive 
            ? 'border-blue-500 bg-blue-50 shadow-sm' 
            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }`}
        onClick={() => onChapterSelect(chapter.id)}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Badge variant="outline" className="text-xs">
                Ch. {chapter.chapterNumber}
              </Badge>
              <Badge 
                variant="outline" 
                className={`text-xs ${statusConfig.color}`}
              >
                <StatusIcon className="h-3 w-3 mr-1" />
                {statusConfig.label}
              </Badge>
            </div>
            
            <h4 className={`font-medium truncate ${
              isActive ? 'text-blue-900' : 'text-gray-900'
            }`}>
              {chapter.title}
            </h4>
            
            {!compact && (
              <div className="flex items-center gap-2 mt-2">
                {chapter.wordCount && (
                  <Badge variant="secondary" className="text-xs">
                    {chapter.wordCount.toLocaleString()} words
                  </Badge>
                )}
                {chapter.summary && (
                  <SummaryBadge
                    summary={chapter.summary}
                    wordCount={chapter.wordCount}
                    glossaryTermCount={chapter.glossaryTerms?.length}
                  />
                )}
              </div>
            )}
          </div>

          {showActions && (
            <div className="flex items-center gap-1 ml-2">
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation();
                  onChapterEdit(chapter.id);
                }}
                title="Edit chapter"
              >
                <Edit className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (compact) {
    return (
      <Card className="w-full">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Chapters</h3>
            <Badge variant="outline" className="text-xs">
              {chapters.length} total
            </Badge>
          </div>
          
          <ScrollArea className="h-[300px]">
            <div className="space-y-2">
              {chapters.map(chapter => 
                renderChapterItem(chapter, chapter.id === currentChapterId)
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Navigation Controls */}
      {currentChapter && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={currentIndex <= 0}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              
              <div className="text-center">
                <div className="text-sm font-medium text-gray-900">
                  Chapter {currentChapter.chapterNumber}
                </div>
                <div className="text-xs text-gray-500">
                  {currentIndex + 1} of {chapters.length}
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleNext}
                disabled={currentIndex >= chapters.length - 1}
                className="flex items-center gap-2"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chapter List */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900 flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              All Chapters
            </h3>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {chapters.filter(c => c.status === 'locked').length} completed
              </Badge>
              <Badge variant="outline" className="text-xs">
                {chapters.length} total
              </Badge>
            </div>
          </div>
          
          <ScrollArea className="h-[500px]">
            <div className="space-y-3">
              {chapters.length === 0 ? (
                <div className="text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No chapters yet</h4>
                  <p className="text-gray-600">
                    Create your book outline to see chapters here
                  </p>
                </div>
              ) : (
                chapters.map(chapter => 
                  renderChapterItem(chapter, chapter.id === currentChapterId)
                )
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium text-gray-900 mb-3">Progress Overview</h3>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Pending</span>
              <Badge variant="outline" className="text-xs">
                {chapters.filter(c => c.status === 'pending').length}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Generating</span>
              <Badge variant="outline" className="text-xs text-blue-600">
                {chapters.filter(c => c.status === 'generating').length}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Drafted</span>
              <Badge variant="outline" className="text-xs text-yellow-600">
                {chapters.filter(c => c.status === 'drafted').length}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Ready</span>
              <Badge variant="outline" className="text-xs text-green-600">
                {chapters.filter(c => c.status === 'summarised').length}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Locked</span>
              <Badge variant="outline" className="text-xs text-purple-600">
                {chapters.filter(c => c.status === 'locked').length}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Total Words</span>
              <Badge variant="outline" className="text-xs">
                {chapters.reduce((sum, c) => sum + (c.wordCount || 0), 0).toLocaleString()}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
