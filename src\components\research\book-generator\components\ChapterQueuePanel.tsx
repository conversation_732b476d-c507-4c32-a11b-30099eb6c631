import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Edit, 
  Lock, 
  Unlock,
  Clock,
  CheckCircle,
  AlertCircle,
  Zap
} from "lucide-react";

import { ChapterState, GenerationQueueItem } from '../types';
import { CHAPTER_STATUS_CONFIG } from '../constants';

interface ChapterQueuePanelProps {
  chapters: ChapterState[];
  queue: GenerationQueueItem[];
  isGenerating: boolean;
  onStartGeneration: () => void;
  onPauseGeneration: () => void;
  onRegenerateChapter: (chapterId: string) => void;
  onEditChapter: (chapterId: string) => void;
  onLockChapter: (chapterId: string) => void;
  onUnlockChapter: (chapterId: string) => void;
}

export function ChapterQueuePanel({
  chapters,
  queue,
  isGenerating,
  onStartGeneration,
  onPauseGeneration,
  onRegenerateChapter,
  onEditChapter,
  onLockChapter,
  onUnlockChapter
}: ChapterQueuePanelProps) {
  
  const getChapterProgress = () => {
    const total = chapters.length;
    const completed = chapters.filter(c => c.status === 'locked').length;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  const getStatusCounts = () => {
    const counts = {
      pending: 0,
      generating: 0,
      drafted: 0,
      summarised: 0,
      locked: 0
    };

    chapters.forEach(chapter => {
      counts[chapter.status]++;
    });

    return counts;
  };

  const statusCounts = getStatusCounts();
  const progress = getChapterProgress();

  const renderChapterItem = (chapter: ChapterState) => {
    const statusConfig = CHAPTER_STATUS_CONFIG[chapter.status];
    const StatusIcon = statusConfig.icon;
    const queueItem = queue.find(q => q.chapterId === chapter.id);

    return (
      <div
        key={chapter.id}
        className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-white hover:shadow-sm transition-all duration-200"
      >
        <div className="flex items-center gap-3 flex-1">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100">
            <span className="text-sm font-medium text-gray-600">
              {chapter.chapterNumber}
            </span>
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 truncate">
              {chapter.title}
            </h4>
            <div className="flex items-center gap-2 mt-1">
              <Badge 
                variant="outline" 
                className={`text-xs ${statusConfig.color}`}
              >
                <StatusIcon className="h-3 w-3 mr-1" />
                {statusConfig.label}
              </Badge>
              {chapter.wordCount && (
                <Badge variant="secondary" className="text-xs">
                  {chapter.wordCount.toLocaleString()} words
                </Badge>
              )}
              {chapter.estimatedReadTime && (
                <Badge variant="secondary" className="text-xs">
                  {chapter.estimatedReadTime} min read
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Progress for generating chapters */}
        {chapter.status === 'generating' && queueItem?.progress && (
          <div className="w-24 mr-3">
            <Progress value={queueItem.progress} className="h-2" />
            <span className="text-xs text-gray-500 mt-1">
              {queueItem.progress}%
            </span>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex items-center gap-1">
          {chapter.status === 'pending' && (
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6"
              onClick={() => onRegenerateChapter(chapter.id)}
              title="Generate chapter"
            >
              <Play className="h-3 w-3" />
            </Button>
          )}
          
          {(chapter.status === 'drafted' || chapter.status === 'summarised') && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-6 w-6"
                onClick={() => onEditChapter(chapter.id)}
                title="Edit chapter"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-6 w-6"
                onClick={() => onRegenerateChapter(chapter.id)}
                title="Regenerate chapter"
              >
                <RotateCcw className="h-3 w-3" />
              </Button>
              {chapter.status === 'summarised' && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-6 w-6 text-green-600 hover:text-green-700"
                  onClick={() => onLockChapter(chapter.id)}
                  title="Lock chapter"
                >
                  <Lock className="h-3 w-3" />
                </Button>
              )}
            </>
          )}
          
          {chapter.status === 'locked' && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-6 w-6"
                onClick={() => onEditChapter(chapter.id)}
                title="Edit chapter"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-6 w-6 text-orange-600 hover:text-orange-700"
                onClick={() => onUnlockChapter(chapter.id)}
                title="Unlock chapter"
              >
                <Unlock className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-green-500 rounded-full"></div>
            Chapter Generation Queue
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {chapters.length} chapters
            </Badge>
            {isGenerating ? (
              <Button
                variant="outline"
                size="sm"
                onClick={onPauseGeneration}
                className="text-orange-600 border-orange-300"
              >
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={onStartGeneration}
                disabled={statusCounts.pending === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Play className="h-4 w-4 mr-2" />
                Start Generation
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Progress Overview */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-blue-900">
              Overall Progress
            </span>
            <span className="text-sm font-medium text-blue-900">
              {progress}%
            </span>
          </div>
          <Progress value={progress} className="h-2 mb-3" />
          
          <div className="grid grid-cols-5 gap-2 text-xs">
            <div className="text-center">
              <div className="font-medium text-gray-600">{statusCounts.pending}</div>
              <div className="text-gray-500">Pending</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-blue-600">{statusCounts.generating}</div>
              <div className="text-gray-500">Generating</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-yellow-600">{statusCounts.drafted}</div>
              <div className="text-gray-500">Drafted</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-green-600">{statusCounts.summarised}</div>
              <div className="text-gray-500">Ready</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-purple-600">{statusCounts.locked}</div>
              <div className="text-gray-500">Locked</div>
            </div>
          </div>
        </div>

        {/* Chapter List */}
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-3">
            {chapters.length === 0 ? (
              <div className="text-center py-12">
                <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No chapters yet</h3>
                <p className="text-gray-600">
                  Create your book outline first to see chapters here
                </p>
              </div>
            ) : (
              chapters.map(renderChapterItem)
            )}
          </div>
        </ScrollArea>

        {/* Generation Status */}
        {isGenerating && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-600 animate-pulse" />
              <span className="text-sm font-medium text-blue-900">
                AI is generating chapters...
              </span>
            </div>
            <p className="text-xs text-blue-700 mt-1">
              This process may take several minutes per chapter
            </p>
          </div>
        )}

        {/* Error Display */}
        {queue.some(q => q.status === 'error') && (
          <div className="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-900">
                Some chapters failed to generate
              </span>
            </div>
            <p className="text-xs text-red-700 mt-1">
              Check individual chapters and try regenerating them
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
