import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  BookOpen, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Filter,
  Download,
  BarChart3,
  Quote
} from "lucide-react";
import { toast } from "sonner";

import { Citation, BookState } from '../types';
import { CITATION_STYLES } from '../constants';
import { useBookStore } from '../hooks/useBookStore';
import citationGraphService from '../services/citation-graph.service';

interface CitationDashboardProps {
  bookState: BookState;
}

export function CitationDashboard({ bookState }: CitationDashboardProps) {
  const { addCitation, updateCitation, deleteCitation } = useBookStore();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterChapter, setFilterChapter] = useState<string>('all');
  const [isAddingCitation, setIsAddingCitation] = useState(false);
  const [editingCitation, setEditingCitation] = useState<Citation | null>(null);
  const [selectedStyle, setSelectedStyle] = useState(bookState.globalSettings.citationStyle);

  // Get all citations
  const allCitations = Object.values(bookState.citationGraph);

  // Filter citations
  const filteredCitations = useMemo(() => {
    let filtered = allCitations;

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(citation =>
        citation.authors.some(author => author.toLowerCase().includes(term)) ||
        citation.title.toLowerCase().includes(term) ||
        citation.journal?.toLowerCase().includes(term) ||
        citation.year.toString().includes(term)
      );
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(citation => citation.type === filterType);
    }

    // Chapter filter
    if (filterChapter !== 'all') {
      const chapterNum = parseInt(filterChapter);
      filtered = filtered.filter(citation => 
        citation.usedInChapters.includes(chapterNum)
      );
    }

    return citationGraphService.sortCitations(filtered);
  }, [allCitations, searchTerm, filterType, filterChapter]);

  // Citation statistics
  const stats = useMemo(() => 
    citationGraphService.getCitationStats(allCitations), 
    [allCitations]
  );

  // Handle citation form
  const [citationForm, setCitationForm] = useState<Partial<Citation>>({
    authors: [''],
    year: new Date().getFullYear(),
    title: '',
    type: 'article',
    journal: '',
    volume: '',
    pages: '',
    doi: '',
    url: ''
  });

  const resetForm = () => {
    setCitationForm({
      authors: [''],
      year: new Date().getFullYear(),
      title: '',
      type: 'article',
      journal: '',
      volume: '',
      pages: '',
      doi: '',
      url: ''
    });
    setIsAddingCitation(false);
    setEditingCitation(null);
  };

  const handleSaveCitation = () => {
    if (!citationForm.authors?.[0] || !citationForm.year || !citationForm.title) {
      toast.error("Please fill in required fields (author, year, title)");
      return;
    }

    const citation: Citation = {
      id: editingCitation?.id || `${citationForm.authors[0].toLowerCase().replace(/[^a-z]/g, '')}${citationForm.year}`,
      key: `(${citationForm.authors[0]}, ${citationForm.year})`,
      title: citationForm.title!,
      authors: citationForm.authors!.filter(a => a.trim()),
      year: citationForm.year!,
      type: citationForm.type as Citation['type'],
      journal: citationForm.journal,
      volume: citationForm.volume,
      pages: citationForm.pages,
      doi: citationForm.doi,
      url: citationForm.url,
      usedInChapters: editingCitation?.usedInChapters || []
    };

    if (editingCitation) {
      updateCitation(citation.id, citation);
      toast.success("Citation updated successfully");
    } else {
      addCitation(citation);
      toast.success("Citation added successfully");
    }

    resetForm();
  };

  const handleEditCitation = (citation: Citation) => {
    setCitationForm(citation);
    setEditingCitation(citation);
    setIsAddingCitation(true);
  };

  const handleDeleteCitation = (citationId: string) => {
    if (confirm("Are you sure you want to delete this citation?")) {
      deleteCitation(citationId);
      toast.success("Citation deleted");
    }
  };

  const exportBibliography = () => {
    const bibliography = citationGraphService.createBibliography(filteredCitations, selectedStyle);
    const content = bibliography.join('\n\n');
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bibliography-${selectedStyle}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    
    toast.success("Bibliography exported successfully");
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl flex items-center gap-3">
              <Quote className="h-6 w-6 text-blue-600" />
              Citation Dashboard
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                onClick={() => setIsAddingCitation(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Citation
              </Button>
              <Button
                variant="outline"
                onClick={exportBibliography}
                disabled={filteredCitations.length === 0}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export Bibliography
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid lg:grid-cols-4 gap-6">
        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Citations</span>
                <Badge variant="outline">{stats.totalCitations}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Chapters with Citations</span>
                <Badge variant="outline">{stats.chaptersWithCitations}</Badge>
              </div>
              {stats.yearRange && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Year Range</span>
                  <Badge variant="outline">
                    {stats.yearRange.earliest}-{stats.yearRange.latest}
                  </Badge>
                </div>
              )}
              
              <div className="pt-2 border-t">
                <h4 className="text-sm font-medium mb-2">By Type</h4>
                {Object.entries(stats.citationsByType).map(([type, count]) => (
                  <div key={type} className="flex justify-between text-xs">
                    <span className="capitalize">{type}</span>
                    <span>{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Citation List */}
        <div className="lg:col-span-3 space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="grid md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search citations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Type</Label>
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="article">Article</SelectItem>
                      <SelectItem value="book">Book</SelectItem>
                      <SelectItem value="conference">Conference</SelectItem>
                      <SelectItem value="website">Website</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Chapter</Label>
                  <Select value={filterChapter} onValueChange={setFilterChapter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Chapters</SelectItem>
                      {bookState.chapters.map(chapter => (
                        <SelectItem key={chapter.id} value={chapter.chapterNumber.toString()}>
                          Chapter {chapter.chapterNumber}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Citation Style</Label>
                  <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CITATION_STYLES.map(style => (
                        <SelectItem key={style.id} value={style.id}>
                          {style.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Citation List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Citations ({filteredCitations.length})</span>
                <Badge variant="outline">
                  {selectedStyle.toUpperCase()} Style
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-4">
                  {filteredCitations.length === 0 ? (
                    <div className="text-center py-12">
                      <Quote className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No citations found</h3>
                      <p className="text-gray-600">
                        {allCitations.length === 0 
                          ? "Add your first citation to get started"
                          : "Try adjusting your search or filters"
                        }
                      </p>
                    </div>
                  ) : (
                    filteredCitations.map(citation => (
                      <div
                        key={citation.id}
                        className="p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="text-xs">
                                {citation.type}
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                {citation.year}
                              </Badge>
                              {citation.usedInChapters.length > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  Ch. {citation.usedInChapters.join(', ')}
                                </Badge>
                              )}
                            </div>
                            
                            <div className="text-sm text-gray-900 mb-1">
                              {citationGraphService.generateReference(citation)}
                            </div>
                            
                            <div className="text-xs text-gray-500">
                              Key: {citation.key}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-1 ml-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditCitation(citation)}
                              className="p-1 h-6 w-6"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteCitation(citation.id)}
                              className="p-1 h-6 w-6 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add/Edit Citation Modal */}
      {isAddingCitation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] bg-white shadow-xl">
            <CardHeader>
              <CardTitle>
                {editingCitation ? 'Edit Citation' : 'Add New Citation'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-4">
                  <div>
                    <Label>Authors *</Label>
                    <Input
                      value={citationForm.authors?.[0] || ''}
                      onChange={(e) => setCitationForm(prev => ({
                        ...prev,
                        authors: [e.target.value, ...(prev.authors?.slice(1) || [])]
                      }))}
                      placeholder="Last name, First name"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Year *</Label>
                      <Input
                        type="number"
                        value={citationForm.year || ''}
                        onChange={(e) => setCitationForm(prev => ({
                          ...prev,
                          year: parseInt(e.target.value) || undefined
                        }))}
                      />
                    </div>
                    <div>
                      <Label>Type</Label>
                      <Select
                        value={citationForm.type}
                        onValueChange={(value) => setCitationForm(prev => ({
                          ...prev,
                          type: value as Citation['type']
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="article">Article</SelectItem>
                          <SelectItem value="book">Book</SelectItem>
                          <SelectItem value="conference">Conference</SelectItem>
                          <SelectItem value="website">Website</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label>Title *</Label>
                    <Input
                      value={citationForm.title || ''}
                      onChange={(e) => setCitationForm(prev => ({
                        ...prev,
                        title: e.target.value
                      }))}
                    />
                  </div>
                  
                  <div>
                    <Label>Journal/Publication</Label>
                    <Input
                      value={citationForm.journal || ''}
                      onChange={(e) => setCitationForm(prev => ({
                        ...prev,
                        journal: e.target.value
                      }))}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Volume</Label>
                      <Input
                        value={citationForm.volume || ''}
                        onChange={(e) => setCitationForm(prev => ({
                          ...prev,
                          volume: e.target.value
                        }))}
                      />
                    </div>
                    <div>
                      <Label>Pages</Label>
                      <Input
                        value={citationForm.pages || ''}
                        onChange={(e) => setCitationForm(prev => ({
                          ...prev,
                          pages: e.target.value
                        }))}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>DOI</Label>
                    <Input
                      value={citationForm.doi || ''}
                      onChange={(e) => setCitationForm(prev => ({
                        ...prev,
                        doi: e.target.value
                      }))}
                    />
                  </div>
                  
                  <div>
                    <Label>URL</Label>
                    <Input
                      value={citationForm.url || ''}
                      onChange={(e) => setCitationForm(prev => ({
                        ...prev,
                        url: e.target.value
                      }))}
                    />
                  </div>
                </div>
              </ScrollArea>
              
              <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
                <Button variant="outline" onClick={resetForm}>
                  Cancel
                </Button>
                <Button onClick={handleSaveCitation}>
                  {editingCitation ? 'Update' : 'Add'} Citation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
