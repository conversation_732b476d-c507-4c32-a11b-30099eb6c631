import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  X,
  BookOpen,
  Hash,
  Download
} from "lucide-react";
import { toast } from "sonner";

import { ChapterState } from '../types';

interface GlossaryTerm {
  term: string;
  definition: string;
  chapters: number[];
}

interface GlossaryManagerProps {
  chapters: ChapterState[];
}

export function GlossaryManager({ chapters }: GlossaryManagerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [editingTerm, setEditingTerm] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    term: '',
    definition: ''
  });

  // Extract and consolidate glossary terms from all chapters
  const glossaryTerms = useMemo(() => {
    const termMap = new Map<string, GlossaryTerm>();
    
    chapters.forEach(chapter => {
      if (chapter.glossaryTerms) {
        chapter.glossaryTerms.forEach(term => {
          const normalizedTerm = term.toLowerCase().trim();
          if (termMap.has(normalizedTerm)) {
            const existing = termMap.get(normalizedTerm)!;
            if (!existing.chapters.includes(chapter.chapterNumber)) {
              existing.chapters.push(chapter.chapterNumber);
            }
          } else {
            termMap.set(normalizedTerm, {
              term: term.trim(),
              definition: '', // Would be filled from a definitions database
              chapters: [chapter.chapterNumber]
            });
          }
        });
      }
    });
    
    return Array.from(termMap.values()).sort((a, b) => 
      a.term.toLowerCase().localeCompare(b.term.toLowerCase())
    );
  }, [chapters]);

  // Filter terms based on search
  const filteredTerms = useMemo(() => {
    if (!searchTerm) return glossaryTerms;
    
    const search = searchTerm.toLowerCase();
    return glossaryTerms.filter(term =>
      term.term.toLowerCase().includes(search) ||
      term.definition.toLowerCase().includes(search)
    );
  }, [glossaryTerms, searchTerm]);

  const resetForm = () => {
    setFormData({ term: '', definition: '' });
    setIsAdding(false);
    setEditingTerm(null);
  };

  const handleSave = () => {
    if (!formData.term.trim() || !formData.definition.trim()) {
      toast.error("Please enter both term and definition");
      return;
    }

    // In a real implementation, this would save to a glossary store
    toast.success(editingTerm ? "Term updated successfully" : "Term added successfully");
    resetForm();
  };

  const handleEdit = (term: GlossaryTerm) => {
    setFormData({
      term: term.term,
      definition: term.definition
    });
    setEditingTerm(term.term);
    setIsAdding(true);
  };

  const handleDelete = (termName: string) => {
    if (confirm(`Are you sure you want to delete the term "${termName}"?`)) {
      // In a real implementation, this would delete from the glossary store
      toast.success("Term deleted");
    }
  };

  const exportGlossary = () => {
    const glossaryContent = filteredTerms
      .map(term => `**${term.term}**: ${term.definition || 'Definition needed'}`)
      .join('\n\n');
    
    const blob = new Blob([glossaryContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'glossary.md';
    a.click();
    URL.revokeObjectURL(url);
    
    toast.success("Glossary exported successfully");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Glossary Manager
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={exportGlossary}
                disabled={filteredTerms.length === 0}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
              <Button
                onClick={() => setIsAdding(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Term
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Search and Stats */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search glossary terms..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="flex items-center gap-1">
                <Hash className="h-3 w-3" />
                {filteredTerms.length} terms
              </Badge>
              <Badge variant="secondary" className="flex items-center gap-1">
                <BookOpen className="h-3 w-3" />
                {chapters.length} chapters
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Terms List */}
      <Card>
        <CardContent className="p-6">
          <ScrollArea className="h-[500px] pr-4">
            <div className="space-y-4">
              {filteredTerms.length === 0 ? (
                <div className="text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {searchTerm ? 'No terms found' : 'No glossary terms yet'}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm 
                      ? 'Try adjusting your search terms'
                      : 'Terms will appear here as you generate chapters with glossary terms'
                    }
                  </p>
                  {!searchTerm && (
                    <Button onClick={() => setIsAdding(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add First Term
                    </Button>
                  )}
                </div>
              ) : (
                filteredTerms.map(term => (
                  <div
                    key={term.term}
                    className="p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium text-gray-900">
                            {term.term}
                          </h4>
                          <div className="flex flex-wrap gap-1">
                            {term.chapters.map(chapterNum => (
                              <Badge key={chapterNum} variant="outline" className="text-xs">
                                Ch. {chapterNum}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600">
                          {term.definition || (
                            <span className="italic text-gray-400">
                              Definition needed - click edit to add
                            </span>
                          )}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(term)}
                          className="p-1 h-6 w-6"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(term.term)}
                          className="p-1 h-6 w-6 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      {isAdding && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-lg bg-white shadow-xl">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  {editingTerm ? 'Edit Term' : 'Add New Term'}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetForm}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="term">Term *</Label>
                  <Input
                    id="term"
                    value={formData.term}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      term: e.target.value
                    }))}
                    placeholder="Enter the term"
                    disabled={!!editingTerm}
                  />
                </div>
                
                <div>
                  <Label htmlFor="definition">Definition *</Label>
                  <textarea
                    id="definition"
                    value={formData.definition}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      definition: e.target.value
                    }))}
                    placeholder="Enter the definition"
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
                <Button variant="outline" onClick={resetForm}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  {editingTerm ? 'Update' : 'Add'} Term
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
