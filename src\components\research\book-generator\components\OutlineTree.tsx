import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  Edit, 
  Trash2, 
  GripVertical,
  FileText,
  Hash
} from "lucide-react";

import { OutlineNode } from '../types';
import { VALIDATION_RULES } from '../constants';

interface OutlineTreeProps {
  outline: OutlineNode[];
  onNodeEdit: (nodeId: string, updates: Partial<OutlineNode>) => void;
  onNodeAdd: (parentId: string | null, level: number) => void;
  onNodeDelete: (nodeId: string) => void;
  readonly?: boolean;
}

interface OutlineNodeItemProps {
  node: OutlineNode;
  level: number;
  onEdit: (nodeId: string, updates: Partial<OutlineNode>) => void;
  onAdd: (parentId: string | null, level: number) => void;
  onDelete: (nodeId: string) => void;
  readonly?: boolean;
}

function OutlineNodeItem({ 
  node, 
  level, 
  onEdit, 
  onAdd, 
  onDelete, 
  readonly = false 
}: OutlineNodeItemProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(node.title);
  const [editDescription, setEditDescription] = useState(node.description || '');
  const [editEstimatedWords, setEditEstimatedWords] = useState(node.estimatedWords || 0);

  const hasChildren = node.children && node.children.length > 0;
  const canAddChildren = level < 3; // Max depth of 3 levels
  const indentLevel = level * 20;

  const handleSaveEdit = () => {
    if (editTitle.trim().length < VALIDATION_RULES.outlineNode.titleMinLength) {
      return;
    }

    onEdit(node.id, {
      title: editTitle.trim(),
      description: editDescription.trim() || undefined,
      estimatedWords: editEstimatedWords || undefined,
    });
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditTitle(node.title);
    setEditDescription(node.description || '');
    setEditEstimatedWords(node.estimatedWords || 0);
    setIsEditing(false);
  };

  const handleAddChild = () => {
    onAdd(node.id, level + 1);
  };

  const handleAddSibling = () => {
    onAdd(null, level); // null parent means sibling
  };

  return (
    <div className="outline-node">
      <Card className="mb-2 border border-gray-200 hover:border-gray-300 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-start gap-3" style={{ marginLeft: `${indentLevel}px` }}>
            {/* Expand/Collapse Button */}
            {hasChildren && (
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-6 w-6"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}

            {/* Drag Handle */}
            {!readonly && (
              <div className="flex items-center">
                <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
              </div>
            )}

            {/* Node Content */}
            <div className="flex-1 min-w-0">
              {isEditing ? (
                <div className="space-y-3">
                  <div>
                    <Input
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      placeholder="Section title"
                      className="font-medium"
                    />
                  </div>
                  <div>
                    <Textarea
                      value={editDescription}
                      onChange={(e) => setEditDescription(e.target.value)}
                      placeholder="Optional description"
                      rows={2}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      value={editEstimatedWords}
                      onChange={(e) => setEditEstimatedWords(parseInt(e.target.value) || 0)}
                      placeholder="Estimated words"
                      className="w-32"
                      min="0"
                    />
                    <span className="text-sm text-gray-500">words</span>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveEdit}>
                      Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline" className="text-xs">
                      {node.numbering}
                    </Badge>
                    <h4 className="font-medium text-gray-900">{node.title}</h4>
                    {node.estimatedWords && (
                      <Badge variant="secondary" className="text-xs">
                        ~{node.estimatedWords} words
                      </Badge>
                    )}
                  </div>
                  {node.description && (
                    <p className="text-sm text-gray-600 mt-1">{node.description}</p>
                  )}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {!readonly && !isEditing && (
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-6 w-6"
                  onClick={() => setIsEditing(true)}
                  title="Edit section"
                >
                  <Edit className="h-3 w-3" />
                </Button>
                {canAddChildren && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-6 w-6"
                    onClick={handleAddChild}
                    title="Add subsection"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-6 w-6 text-red-600 hover:text-red-700"
                  onClick={() => onDelete(node.id)}
                  title="Delete section"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-4">
          {node.children!.map((child) => (
            <OutlineNodeItem
              key={child.id}
              node={child}
              level={level + 1}
              onEdit={onEdit}
              onAdd={onAdd}
              onDelete={onDelete}
              readonly={readonly}
            />
          ))}
        </div>
      )}

      {/* Add Sibling Button */}
      {!readonly && level === 1 && (
        <div className="mt-2" style={{ marginLeft: `${indentLevel}px` }}>
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddSibling}
            className="border-dashed"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Chapter
          </Button>
        </div>
      )}
    </div>
  );
}

export function OutlineTree({ 
  outline, 
  onNodeEdit, 
  onNodeAdd, 
  onNodeDelete, 
  readonly = false 
}: OutlineTreeProps) {
  const totalEstimatedWords = outline.reduce((total, node) => {
    const nodeWords = node.estimatedWords || 0;
    const childWords = node.children?.reduce((childTotal, child) => 
      childTotal + (child.estimatedWords || 0), 0) || 0;
    return total + nodeWords + childWords;
  }, 0);

  const handleAddRootNode = () => {
    onNodeAdd(null, 1);
  };

  return (
    <div className="outline-tree">
      {/* Summary Stats */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                {outline.length} Chapters
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                ~{totalEstimatedWords.toLocaleString()} Words
              </span>
            </div>
          </div>
          {!readonly && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddRootNode}
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Chapter
            </Button>
          )}
        </div>
      </div>

      {/* Outline Nodes */}
      <div className="space-y-2">
        {outline.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No chapters yet</h3>
            <p className="text-gray-600 mb-4">Start building your book outline by adding chapters</p>
            {!readonly && (
              <Button onClick={handleAddRootNode}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Chapter
              </Button>
            )}
          </div>
        ) : (
          outline.map((node) => (
            <OutlineNodeItem
              key={node.id}
              node={node}
              level={1}
              onEdit={onNodeEdit}
              onAdd={onNodeAdd}
              onDelete={onNodeDelete}
              readonly={readonly}
            />
          ))
        )}
      </div>
    </div>
  );
}
