import React, { useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  FileText, 
  Hash, 
  Clock, 
  Eye, 
  X,
  BookOpen
} from "lucide-react";

import { SummaryBadgeProps } from '../types';

export function SummaryBadge({ 
  summary, 
  wordCount, 
  glossaryTermCount, 
  onClick 
}: SummaryBadgeProps) {
  const [showPreview, setShowPreview] = useState(false);

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      setShowPreview(true);
    }
  };

  const summaryWordCount = summary 
    ? summary.split(/\s+/).filter(word => word.length > 0).length 
    : 0;

  const readingTime = Math.ceil(summaryWordCount / 200); // 200 words per minute

  if (!summary) {
    return (
      <Badge variant="outline" className="text-xs text-gray-500">
        <FileText className="h-3 w-3 mr-1" />
        No summary
      </Badge>
    );
  }

  return (
    <>
      <Badge 
        variant="secondary" 
        className="text-xs cursor-pointer hover:bg-blue-100 transition-colors"
        onClick={handleClick}
      >
        <FileText className="h-3 w-3 mr-1" />
        Summary ({summaryWordCount} words)
      </Badge>

      {/* Summary Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[80vh] bg-white shadow-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Chapter Summary
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreview(false)}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            
            <CardContent>
              {/* Summary Stats */}
              <div className="flex items-center gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Hash className="h-3 w-3" />
                  <span>{summaryWordCount} words</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Clock className="h-3 w-3" />
                  <span>{readingTime} min read</span>
                </div>
                {glossaryTermCount !== undefined && (
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <FileText className="h-3 w-3" />
                    <span>{glossaryTermCount} terms</span>
                  </div>
                )}
                {wordCount !== undefined && (
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <BookOpen className="h-3 w-3" />
                    <span>{wordCount.toLocaleString()} total words</span>
                  </div>
                )}
              </div>

              {/* Summary Content */}
              <ScrollArea className="h-[400px] pr-4">
                <div className="prose prose-sm max-w-none">
                  <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                    {summary}
                  </div>
                </div>
              </ScrollArea>

              {/* Actions */}
              <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setShowPreview(false)}
                >
                  Close
                </Button>
                {onClick && (
                  <Button
                    onClick={() => {
                      setShowPreview(false);
                      onClick();
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Full Chapter
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}

// Compact version for use in lists
export function CompactSummaryBadge({ 
  summary, 
  wordCount, 
  glossaryTermCount 
}: Omit<SummaryBadgeProps, 'onClick'>) {
  if (!summary) {
    return (
      <Badge variant="outline" className="text-xs text-gray-400">
        No summary
      </Badge>
    );
  }

  const summaryWordCount = summary.split(/\s+/).filter(word => word.length > 0).length;

  return (
    <Badge variant="secondary" className="text-xs">
      {summaryWordCount}w summary
    </Badge>
  );
}

// Summary status indicator
export function SummaryStatusBadge({ 
  summary, 
  status 
}: { 
  summary: string | null; 
  status: 'pending' | 'generating' | 'completed' | 'error' 
}) {
  if (status === 'generating') {
    return (
      <Badge variant="outline" className="text-xs text-blue-600 border-blue-300">
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse mr-1" />
        Generating summary...
      </Badge>
    );
  }

  if (status === 'error') {
    return (
      <Badge variant="outline" className="text-xs text-red-600 border-red-300">
        Summary failed
      </Badge>
    );
  }

  if (!summary) {
    return (
      <Badge variant="outline" className="text-xs text-gray-500">
        No summary
      </Badge>
    );
  }

  const wordCount = summary.split(/\s+/).filter(word => word.length > 0).length;

  return (
    <Badge variant="secondary" className="text-xs text-green-700 bg-green-100">
      <FileText className="h-3 w-3 mr-1" />
      {wordCount}w summary
    </Badge>
  );
}
