import { 
  BookOpen, 
  FileText, 
  Users, 
  Target, 
  Lightbulb,
  BarChart3,
  FlaskConical,
  Zap,
  Globe,
  TrendingUp,
  CheckCircle,
  ArrowRight
} from "lucide-react";

// Book generation settings
export const DEFAULT_BOOK_SETTINGS = {
  maxWordsPerChapter: 6000,
  summaryMaxWords: 400,
  minOutlineNodes: 10,
  maxOutlineDepth: 3,
  defaultTone: 'professional' as const,
  defaultCitationStyle: 'apa' as const,
};

// AI Models available for book generation
export const AI_MODELS = [
  {
    id: "google/gemini-2.5-flash-lite-preview-06-17",
    name: "Gemini 2.5 Flash",
    description: "Fast and efficient for most content",
    maxTokens: 8192,
    recommended: true
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    description: "High-quality writing and analysis",
    maxTokens: 8192,
    recommended: false
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    description: "Excellent for creative and technical writing",
    maxTokens: 4096,
    recommended: false
  }
];

// Book genres and categories
export const BOOK_GENRES = [
  { id: 'academic', name: 'Academic/Research', icon: BookOpen },
  { id: 'business', name: 'Business/Professional', icon: TrendingUp },
  { id: 'technical', name: 'Technical/How-to', icon: FlaskConical },
  { id: 'self-help', name: 'Self-Help/Personal Development', icon: Target },
  { id: 'biography', name: 'Biography/Memoir', icon: Users },
  { id: 'history', name: 'History/Non-fiction', icon: Globe },
  { id: 'science', name: 'Science/Research', icon: BarChart3 },
  { id: 'creative', name: 'Creative/Arts', icon: Lightbulb },
  { id: 'other', name: 'Other', icon: FileText }
];

// Writing tones
export const WRITING_TONES = [
  { 
    id: 'academic', 
    name: 'Academic', 
    description: 'Formal, scholarly, with citations and references' 
  },
  { 
    id: 'professional', 
    name: 'Professional', 
    description: 'Business-appropriate, clear and authoritative' 
  },
  { 
    id: 'casual', 
    name: 'Casual', 
    description: 'Conversational, accessible, and engaging' 
  },
  { 
    id: 'technical', 
    name: 'Technical', 
    description: 'Precise, detailed, with technical terminology' 
  }
];

// Citation styles
export const CITATION_STYLES = [
  { id: 'apa', name: 'APA', description: 'American Psychological Association' },
  { id: 'mla', name: 'MLA', description: 'Modern Language Association' },
  { id: 'chicago', name: 'Chicago', description: 'Chicago Manual of Style' },
  { id: 'ieee', name: 'IEEE', description: 'Institute of Electrical and Electronics Engineers' }
];

// Chapter status configurations
export const CHAPTER_STATUS_CONFIG = {
  pending: {
    label: 'Pending',
    color: 'bg-gray-100 text-gray-600',
    icon: ArrowRight,
    description: 'Waiting to be generated'
  },
  generating: {
    label: 'Generating',
    color: 'bg-blue-100 text-blue-600',
    icon: Zap,
    description: 'AI is writing the chapter'
  },
  drafted: {
    label: 'Drafted',
    color: 'bg-yellow-100 text-yellow-600',
    icon: FileText,
    description: 'Chapter written, needs summary'
  },
  summarised: {
    label: 'Ready to Edit',
    color: 'bg-green-100 text-green-600',
    icon: CheckCircle,
    description: 'Chapter complete with summary'
  },
  locked: {
    label: 'Locked',
    color: 'bg-purple-100 text-purple-600',
    icon: CheckCircle,
    description: 'Chapter finalized and locked'
  }
};

// Default outline structure templates
export const OUTLINE_TEMPLATES = {
  academic: [
    { title: 'Introduction', level: 1, estimatedWords: 800 },
    { title: 'Literature Review', level: 1, estimatedWords: 1200 },
    { title: 'Methodology', level: 1, estimatedWords: 1000 },
    { title: 'Results and Analysis', level: 1, estimatedWords: 1500 },
    { title: 'Discussion', level: 1, estimatedWords: 1200 },
    { title: 'Conclusion', level: 1, estimatedWords: 600 }
  ],
  business: [
    { title: 'Executive Summary', level: 1, estimatedWords: 500 },
    { title: 'Market Analysis', level: 1, estimatedWords: 1200 },
    { title: 'Strategy Framework', level: 1, estimatedWords: 1500 },
    { title: 'Implementation Plan', level: 1, estimatedWords: 1800 },
    { title: 'Risk Assessment', level: 1, estimatedWords: 1000 },
    { title: 'Future Outlook', level: 1, estimatedWords: 800 }
  ],
  technical: [
    { title: 'Overview and Prerequisites', level: 1, estimatedWords: 600 },
    { title: 'Fundamentals', level: 1, estimatedWords: 1500 },
    { title: 'Core Concepts', level: 1, estimatedWords: 2000 },
    { title: 'Advanced Techniques', level: 1, estimatedWords: 2200 },
    { title: 'Best Practices', level: 1, estimatedWords: 1200 },
    { title: 'Troubleshooting and FAQ', level: 1, estimatedWords: 800 }
  ]
};

// Export format configurations
export const EXPORT_FORMATS = [
  {
    id: 'pdf',
    name: 'PDF',
    description: 'Portable Document Format - best for sharing and printing',
    icon: FileText,
    features: ['Page numbers', 'Table of contents', 'Bookmarks', 'Print-ready']
  },
  {
    id: 'epub',
    name: 'EPUB',
    description: 'Electronic Publication - ideal for e-readers',
    icon: BookOpen,
    features: ['Reflowable text', 'E-reader compatible', 'Adjustable fonts', 'Bookmarks']
  },
  {
    id: 'latex',
    name: 'LaTeX',
    description: 'LaTeX source code - for academic publishing',
    icon: FlaskConical,
    features: ['Academic formatting', 'Citation management', 'Mathematical formulas', 'Professional typesetting']
  },
  {
    id: 'docx',
    name: 'Word Document',
    description: 'Microsoft Word format - for further editing',
    icon: FileText,
    features: ['Editable format', 'Track changes', 'Comments', 'Collaboration']
  }
];

// Validation rules
export const VALIDATION_RULES = {
  title: {
    minLength: 3,
    maxLength: 200,
    required: true
  },
  chapterTitle: {
    minLength: 3,
    maxLength: 100,
    required: true
  },
  authors: {
    minItems: 1,
    maxItems: 10,
    required: true
  },
  keywords: {
    minItems: 1,
    maxItems: 20,
    required: false
  },
  description: {
    maxLength: 1000,
    required: false
  },
  outlineNode: {
    titleMinLength: 3,
    titleMaxLength: 150,
    descriptionMaxLength: 500
  }
};

// Default book metadata
export const DEFAULT_BOOK_META = {
  title: '',
  subtitle: '',
  authors: [],
  genre: 'professional',
  keywords: [],
  description: '',
  targetAudience: '',
  estimatedLength: 200
};

// Context building settings
export const CONTEXT_SETTINGS = {
  maxPreviousChapterWords: 8000, // Truncate if longer
  maxSummaryWords: 400,
  maxTotalContextWords: 12000, // Total context limit
  includeGlossaryTerms: true,
  includeFigureReferences: true
};
