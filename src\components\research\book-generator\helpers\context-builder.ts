import { ChapterState, ContextPack, BookMeta } from '../types';
import { CONTEXT_SETTINGS } from '../constants';

/**
 * Utility functions for building and managing context for chapter generation
 */

/**
 * Build a context pack for chapter generation
 */
export function buildChapterContext(
  chapterIndex: number,
  chapters: ChapterState[],
  bookMeta: BookMeta
): ContextPack {
  const currentChapterNumber = chapterIndex + 1;
  const totalChapters = chapters.length;

  // Get previous chapter (full content)
  let previousFull = '';
  if (chapterIndex > 0) {
    const previousChapter = chapters[chapterIndex - 1];
    if (previousChapter?.draft) {
      previousFull = truncateToWordLimit(
        previousChapter.draft,
        CONTEXT_SETTINGS.maxPreviousChapterWords
      );
    }
  }

  // Get summaries of all earlier chapters
  const priorSummaries: string[] = [];
  for (let i = 0; i < chapterIndex; i++) {
    const chapter = chapters[i];
    if (chapter?.summary) {
      priorSummaries.push(chapter.summary);
    }
  }

  // Collect glossary terms from previous chapters
  const glossaryTerms: string[] = [];
  if (CONTEXT_SETTINGS.includeGlossaryTerms) {
    for (let i = 0; i < chapterIndex; i++) {
      const chapter = chapters[i];
      if (chapter?.glossaryTerms) {
        glossaryTerms.push(...chapter.glossaryTerms);
      }
    }
  }

  return {
    previousFull,
    priorSummaries,
    bookMeta,
    currentChapterNumber,
    totalChapters,
    glossaryTerms: [...new Set(glossaryTerms)], // Remove duplicates
  };
}

/**
 * Format context pack into a string suitable for AI prompts
 */
export function formatContextForPrompt(context: ContextPack): string {
  let formattedContext = '';

  // Add book metadata
  formattedContext += `BOOK CONTEXT:\n`;
  formattedContext += `Title: "${context.bookMeta.title}"\n`;
  if (context.bookMeta.subtitle) {
    formattedContext += `Subtitle: "${context.bookMeta.subtitle}"\n`;
  }
  formattedContext += `Authors: <AUTHORS>
  formattedContext += `Genre: ${context.bookMeta.genre}\n`;
  if (context.bookMeta.description) {
    formattedContext += `Description: ${context.bookMeta.description}\n`;
  }
  formattedContext += `\nThis is Chapter ${context.currentChapterNumber} of ${context.totalChapters} total chapters.\n\n`;

  // Add previous chapter content if available
  if (context.previousFull) {
    formattedContext += `PREVIOUS CHAPTER (Chapter ${context.currentChapterNumber - 1}) CONTENT:\n`;
    formattedContext += `${context.previousFull}\n\n`;
  }

  // Add summaries of earlier chapters
  if (context.priorSummaries.length > 0) {
    formattedContext += `SUMMARIES OF EARLIER CHAPTERS:\n`;
    context.priorSummaries.forEach((summary, index) => {
      formattedContext += `\nChapter ${index + 1} Summary:\n${summary}\n`;
    });
    formattedContext += '\n';
  }

  // Add glossary terms from previous chapters
  if (context.glossaryTerms.length > 0) {
    formattedContext += `KEY TERMS ESTABLISHED IN PREVIOUS CHAPTERS:\n`;
    formattedContext += context.glossaryTerms.join(', ') + '\n\n';
  }

  return formattedContext;
}

/**
 * Calculate the total word count of a context pack
 */
export function calculateContextWordCount(context: ContextPack): number {
  const previousWords = context.previousFull 
    ? countWords(context.previousFull) 
    : 0;
  
  const summariesWords = context.priorSummaries
    .map(summary => countWords(summary))
    .reduce((sum, count) => sum + count, 0);

  return previousWords + summariesWords;
}

/**
 * Validate if a context pack is within acceptable limits
 */
export function validateContextLimits(context: ContextPack): {
  isValid: boolean;
  wordCount: number;
  issues: string[];
} {
  const wordCount = calculateContextWordCount(context);
  const issues: string[] = [];

  if (wordCount > CONTEXT_SETTINGS.maxTotalContextWords) {
    issues.push(`Context exceeds maximum word limit (${wordCount}/${CONTEXT_SETTINGS.maxTotalContextWords} words)`);
  }

  // Check if previous chapter is too long
  if (context.previousFull) {
    const previousWords = countWords(context.previousFull);
    if (previousWords > CONTEXT_SETTINGS.maxPreviousChapterWords) {
      issues.push(`Previous chapter content is too long (${previousWords}/${CONTEXT_SETTINGS.maxPreviousChapterWords} words)`);
    }
  }

  // Check if any summary is too long
  context.priorSummaries.forEach((summary, index) => {
    const summaryWords = countWords(summary);
    if (summaryWords > CONTEXT_SETTINGS.maxSummaryWords) {
      issues.push(`Chapter ${index + 1} summary is too long (${summaryWords}/${CONTEXT_SETTINGS.maxSummaryWords} words)`);
    }
  });

  return {
    isValid: issues.length === 0,
    wordCount,
    issues
  };
}

/**
 * Optimize context by truncating content to fit within limits
 */
export function optimizeContext(context: ContextPack): ContextPack {
  const validation = validateContextLimits(context);
  
  if (validation.isValid) {
    return context; // No optimization needed
  }

  const optimized = { ...context };

  // First, truncate previous chapter if it's too long
  if (optimized.previousFull) {
    const previousWords = countWords(optimized.previousFull);
    if (previousWords > CONTEXT_SETTINGS.maxPreviousChapterWords) {
      optimized.previousFull = truncateToWordLimit(
        optimized.previousFull,
        CONTEXT_SETTINGS.maxPreviousChapterWords
      );
    }
  }

  // Then, truncate summaries if needed
  optimized.priorSummaries = optimized.priorSummaries.map(summary => {
    const summaryWords = countWords(summary);
    if (summaryWords > CONTEXT_SETTINGS.maxSummaryWords) {
      return truncateToWordLimit(summary, CONTEXT_SETTINGS.maxSummaryWords);
    }
    return summary;
  });

  // If still over limit, remove oldest summaries
  while (calculateContextWordCount(optimized) > CONTEXT_SETTINGS.maxTotalContextWords && optimized.priorSummaries.length > 1) {
    optimized.priorSummaries.shift(); // Remove oldest summary
  }

  return optimized;
}

/**
 * Create a minimal context pack for the first chapter
 */
export function createFirstChapterContext(bookMeta: BookMeta, totalChapters: number): ContextPack {
  return {
    previousFull: '',
    priorSummaries: [],
    bookMeta,
    currentChapterNumber: 1,
    totalChapters,
    glossaryTerms: [],
  };
}

/**
 * Get context statistics for analysis
 */
export function getContextStats(context: ContextPack) {
  const previousWords = context.previousFull ? countWords(context.previousFull) : 0;
  const summariesWords = context.priorSummaries
    .map(summary => countWords(summary))
    .reduce((sum, count) => sum + count, 0);
  const totalWords = previousWords + summariesWords;

  return {
    hasPreviousChapter: !!context.previousFull,
    previousChapterWords: previousWords,
    summariesCount: context.priorSummaries.length,
    summariesWords,
    totalWords,
    glossaryTermsCount: context.glossaryTerms.length,
    isWithinLimits: totalWords <= CONTEXT_SETTINGS.maxTotalContextWords,
    utilizationPercentage: Math.round((totalWords / CONTEXT_SETTINGS.maxTotalContextWords) * 100),
  };
}

// Utility functions

/**
 * Count words in a text string
 */
function countWords(text: string): number {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Truncate text to a specific word limit
 */
function truncateToWordLimit(text: string, maxWords: number): string {
  const words = text.trim().split(/\s+/);
  
  if (words.length <= maxWords) {
    return text;
  }
  
  const truncated = words.slice(0, maxWords).join(' ');
  return truncated + '\n\n[Content truncated to fit context limits...]';
}

/**
 * Extract key sentences from text for summarization
 */
export function extractKeySentences(text: string, maxSentences: number = 3): string[] {
  const sentences = text
    .split(/[.!?]+/)
    .map(s => s.trim())
    .filter(s => s.length > 20); // Filter out very short sentences

  if (sentences.length <= maxSentences) {
    return sentences;
  }

  // Simple extraction: take first, middle, and last sentences
  const indices = [
    0,
    Math.floor(sentences.length / 2),
    sentences.length - 1
  ].slice(0, maxSentences);

  return indices.map(i => sentences[i]);
}
