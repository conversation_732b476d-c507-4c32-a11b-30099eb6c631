import { Figure } from '../types';

/**
 * Utility functions for managing figures across chapters
 */

export interface FigureReference {
  id: string;
  chapterNumber: number;
  figureNumber: number;
  globalNumber: string;
  title: string;
  type: Figure['type'];
}

/**
 * Generate global figure numbering across all chapters
 */
export function generateGlobalFigureNumbers(figures: Figure[]): Figure[] {
  // Sort figures by chapter and then by figure number within chapter
  const sortedFigures = [...figures].sort((a, b) => {
    if (a.chapterNumber !== b.chapterNumber) {
      return a.chapterNumber - b.chapterNumber;
    }
    return a.figureNumber - b.figureNumber;
  });

  // Generate global numbers
  return sortedFigures.map(figure => ({
    ...figure,
    globalNumber: `Fig ${figure.chapterNumber}-${figure.figureNumber}`
  }));
}

/**
 * Extract figure references from markdown content
 */
export function extractFigureReferences(content: string, chapterNumber: number): FigureReference[] {
  const references: FigureReference[] = [];
  
  // Pattern to match figure references like ![Figure 1: Title](url)
  const figurePattern = /!\[(?:Figure\s+(\d+):\s*)?([^\]]+)\]\(([^)]+)\)/gi;
  
  let match;
  let figureCounter = 1;
  
  while ((match = figurePattern.exec(content)) !== null) {
    const [, figureNum, title, url] = match;
    const figureNumber = figureNum ? parseInt(figureNum) : figureCounter++;
    
    references.push({
      id: `fig-${chapterNumber}-${figureNumber}`,
      chapterNumber,
      figureNumber,
      globalNumber: `Fig ${chapterNumber}-${figureNumber}`,
      title: title.trim(),
      type: inferFigureType(title, url)
    });
  }
  
  return references;
}

/**
 * Update figure references in content with global numbering
 */
export function updateFigureReferences(
  content: string, 
  figureMap: Map<string, string>
): string {
  let updatedContent = content;
  
  // Replace figure references with global numbers
  figureMap.forEach((globalNumber, localReference) => {
    const pattern = new RegExp(`\\b${localReference}\\b`, 'gi');
    updatedContent = updatedContent.replace(pattern, globalNumber);
  });
  
  return updatedContent;
}

/**
 * Generate a list of figures for the book
 */
export function generateFigureList(figures: Figure[]): string {
  const sortedFigures = generateGlobalFigureNumbers(figures);
  
  let figureList = '# List of Figures\n\n';
  
  sortedFigures.forEach(figure => {
    figureList += `${figure.globalNumber}: ${figure.title}\n`;
  });
  
  return figureList;
}

/**
 * Validate figure numbering consistency
 */
export function validateFigureNumbering(figures: Figure[]): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  const chapterFigures = new Map<number, Figure[]>();
  
  // Group figures by chapter
  figures.forEach(figure => {
    if (!chapterFigures.has(figure.chapterNumber)) {
      chapterFigures.set(figure.chapterNumber, []);
    }
    chapterFigures.get(figure.chapterNumber)!.push(figure);
  });
  
  // Check numbering within each chapter
  chapterFigures.forEach((chapterFigs, chapterNum) => {
    const sortedFigs = chapterFigs.sort((a, b) => a.figureNumber - b.figureNumber);
    
    for (let i = 0; i < sortedFigs.length; i++) {
      const expectedNumber = i + 1;
      const actualNumber = sortedFigs[i].figureNumber;
      
      if (actualNumber !== expectedNumber) {
        issues.push(
          `Chapter ${chapterNum}: Figure numbering gap - expected ${expectedNumber}, found ${actualNumber}`
        );
      }
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * Renumber figures to ensure consistency
 */
export function renumberFigures(figures: Figure[]): Figure[] {
  const chapterFigures = new Map<number, Figure[]>();
  
  // Group figures by chapter
  figures.forEach(figure => {
    if (!chapterFigures.has(figure.chapterNumber)) {
      chapterFigures.set(figure.chapterNumber, []);
    }
    chapterFigures.get(figure.chapterNumber)!.push(figure);
  });
  
  const renumberedFigures: Figure[] = [];
  
  // Renumber within each chapter
  chapterFigures.forEach((chapterFigs, chapterNum) => {
    const sortedFigs = chapterFigs.sort((a, b) => a.figureNumber - b.figureNumber);
    
    sortedFigs.forEach((figure, index) => {
      const newFigureNumber = index + 1;
      renumberedFigures.push({
        ...figure,
        figureNumber: newFigureNumber,
        globalNumber: `Fig ${chapterNum}-${newFigureNumber}`
      });
    });
  });
  
  return renumberedFigures;
}

/**
 * Create figure cross-reference map
 */
export function createFigureCrossReferenceMap(figures: Figure[]): Map<string, string[]> {
  const crossRefs = new Map<string, string[]>();
  
  figures.forEach(figure => {
    const key = figure.globalNumber;
    const references: string[] = [];
    
    // Find chapters that might reference this figure
    figures.forEach(otherFigure => {
      if (otherFigure.id !== figure.id && 
          otherFigure.chapterNumber !== figure.chapterNumber) {
        // This is a simplified check - in practice, you'd scan content for references
        references.push(`Chapter ${otherFigure.chapterNumber}`);
      }
    });
    
    if (references.length > 0) {
      crossRefs.set(key, [...new Set(references)]);
    }
  });
  
  return crossRefs;
}

/**
 * Generate figure statistics
 */
export function generateFigureStatistics(figures: Figure[]) {
  const stats = {
    totalFigures: figures.length,
    figuresByChapter: new Map<number, number>(),
    figuresByType: new Map<string, number>(),
    averageFiguresPerChapter: 0,
    chaptersWithFigures: new Set<number>()
  };
  
  figures.forEach(figure => {
    // Count by chapter
    const chapterCount = stats.figuresByChapter.get(figure.chapterNumber) || 0;
    stats.figuresByChapter.set(figure.chapterNumber, chapterCount + 1);
    stats.chaptersWithFigures.add(figure.chapterNumber);
    
    // Count by type
    const typeCount = stats.figuresByType.get(figure.type) || 0;
    stats.figuresByType.set(figure.type, typeCount + 1);
  });
  
  // Calculate average
  if (stats.chaptersWithFigures.size > 0) {
    stats.averageFiguresPerChapter = stats.totalFigures / stats.chaptersWithFigures.size;
  }
  
  return stats;
}

/**
 * Export figure registry to various formats
 */
export function exportFigureRegistry(
  figures: Figure[],
  format: 'markdown' | 'html' | 'json' | 'csv' = 'markdown'
): string {
  const sortedFigures = generateGlobalFigureNumbers(figures);
  
  switch (format) {
    case 'markdown':
      return exportFiguresToMarkdown(sortedFigures);
    case 'html':
      return exportFiguresToHTML(sortedFigures);
    case 'json':
      return JSON.stringify(sortedFigures, null, 2);
    case 'csv':
      return exportFiguresToCSV(sortedFigures);
    default:
      return exportFiguresToMarkdown(sortedFigures);
  }
}

// Helper functions

function inferFigureType(title: string, url: string): Figure['type'] {
  const titleLower = title.toLowerCase();
  const urlLower = url.toLowerCase();
  
  if (titleLower.includes('table') || titleLower.includes('data')) {
    return 'table';
  }
  
  if (titleLower.includes('chart') || titleLower.includes('graph') || 
      titleLower.includes('plot') || urlLower.includes('chart')) {
    return 'chart';
  }
  
  if (titleLower.includes('diagram') || titleLower.includes('flow') || 
      titleLower.includes('schema')) {
    return 'diagram';
  }
  
  return 'image';
}

function exportFiguresToMarkdown(figures: Figure[]): string {
  let markdown = '# List of Figures\n\n';
  
  figures.forEach(figure => {
    markdown += `**${figure.globalNumber}**: ${figure.title}\n`;
    if (figure.caption) {
      markdown += `*${figure.caption}*\n`;
    }
    markdown += `Type: ${figure.type}\n\n`;
  });
  
  return markdown;
}

function exportFiguresToHTML(figures: Figure[]): string {
  let html = '<h1>List of Figures</h1>\n<ol>\n';
  
  figures.forEach(figure => {
    html += `  <li>\n`;
    html += `    <strong>${figure.globalNumber}</strong>: ${figure.title}\n`;
    if (figure.caption) {
      html += `    <br><em>${figure.caption}</em>\n`;
    }
    html += `    <br>Type: ${figure.type}\n`;
    html += `  </li>\n`;
  });
  
  html += '</ol>';
  return html;
}

function exportFiguresToCSV(figures: Figure[]): string {
  let csv = 'Global Number,Chapter,Figure Number,Title,Caption,Type\n';
  
  figures.forEach(figure => {
    const title = (figure.title || '').replace(/"/g, '""');
    const caption = (figure.caption || '').replace(/"/g, '""');
    csv += `"${figure.globalNumber}",${figure.chapterNumber},${figure.figureNumber},"${title}","${caption}","${figure.type}"\n`;
  });
  
  return csv;
}
