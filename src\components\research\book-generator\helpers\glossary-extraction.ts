/**
 * Utility functions for extracting and managing glossary terms
 */

export interface GlossaryTerm {
  term: string;
  definition?: string;
  frequency: number;
  chapters: number[];
  context: string[];
}

/**
 * Extract potential glossary terms from text content
 */
export function extractGlossaryTerms(
  content: string, 
  chapterNumber: number,
  options: {
    minLength?: number;
    maxLength?: number;
    excludeCommonWords?: boolean;
    includeAcronyms?: boolean;
  } = {}
): GlossaryTerm[] {
  const {
    minLength = 3,
    maxLength = 50,
    excludeCommonWords = true,
    includeAcronyms = true
  } = options;

  const terms: Map<string, GlossaryTerm> = new Map();

  // Extract terms in quotes
  const quotedTerms = content.match(/"([^"]+)"/g);
  if (quotedTerms) {
    quotedTerms.forEach(match => {
      const term = match.slice(1, -1).trim();
      if (isValidTerm(term, minLength, maxLength)) {
        addTerm(terms, term, chapterNumber, 'quoted');
      }
    });
  }

  // Extract capitalized terms (potential proper nouns/concepts)
  const capitalizedTerms = content.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g);
  if (capitalizedTerms) {
    capitalizedTerms.forEach(term => {
      if (isValidTerm(term, minLength, maxLength) && 
          (!excludeCommonWords || !isCommonWord(term))) {
        addTerm(terms, term, chapterNumber, 'capitalized');
      }
    });
  }

  // Extract acronyms
  if (includeAcronyms) {
    const acronyms = content.match(/\b[A-Z]{2,}\b/g);
    if (acronyms) {
      acronyms.forEach(term => {
        if (isValidTerm(term, 2, 10)) {
          addTerm(terms, term, chapterNumber, 'acronym');
        }
      });
    }
  }

  // Extract technical terms (words with specific suffixes)
  const technicalSuffixes = /\b\w+(?:tion|sion|ment|ness|ity|ism|ology|graphy|ics|ism)\b/gi;
  const technicalTerms = content.match(technicalSuffixes);
  if (technicalTerms) {
    technicalTerms.forEach(term => {
      if (isValidTerm(term, minLength, maxLength) && 
          (!excludeCommonWords || !isCommonWord(term))) {
        addTerm(terms, term, chapterNumber, 'technical');
      }
    });
  }

  return Array.from(terms.values()).sort((a, b) => b.frequency - a.frequency);
}

/**
 * Merge glossary terms from multiple chapters
 */
export function mergeGlossaryTerms(termLists: GlossaryTerm[][]): GlossaryTerm[] {
  const mergedTerms: Map<string, GlossaryTerm> = new Map();

  termLists.forEach(terms => {
    terms.forEach(term => {
      const key = term.term.toLowerCase();
      
      if (mergedTerms.has(key)) {
        const existing = mergedTerms.get(key)!;
        existing.frequency += term.frequency;
        existing.chapters = [...new Set([...existing.chapters, ...term.chapters])];
        existing.context = [...existing.context, ...term.context];
      } else {
        mergedTerms.set(key, { ...term });
      }
    });
  });

  return Array.from(mergedTerms.values()).sort((a, b) => b.frequency - a.frequency);
}

/**
 * Filter glossary terms by relevance and frequency
 */
export function filterGlossaryTerms(
  terms: GlossaryTerm[],
  options: {
    minFrequency?: number;
    maxTerms?: number;
    excludePatterns?: RegExp[];
  } = {}
): GlossaryTerm[] {
  const {
    minFrequency = 1,
    maxTerms = 50,
    excludePatterns = []
  } = options;

  let filtered = terms.filter(term => {
    // Check minimum frequency
    if (term.frequency < minFrequency) return false;
    
    // Check exclude patterns
    if (excludePatterns.some(pattern => pattern.test(term.term))) return false;
    
    return true;
  });

  // Limit to max terms
  if (maxTerms > 0) {
    filtered = filtered.slice(0, maxTerms);
  }

  return filtered;
}

/**
 * Generate definitions for glossary terms using context
 */
export function generateDefinitions(terms: GlossaryTerm[]): GlossaryTerm[] {
  return terms.map(term => {
    if (term.definition) return term;

    // Try to extract definition from context
    const definition = extractDefinitionFromContext(term.term, term.context);
    
    return {
      ...term,
      definition: definition || `Definition needed for "${term.term}"`
    };
  });
}

/**
 * Export glossary to various formats
 */
export function exportGlossary(
  terms: GlossaryTerm[],
  format: 'markdown' | 'html' | 'json' | 'csv' = 'markdown'
): string {
  switch (format) {
    case 'markdown':
      return exportToMarkdown(terms);
    case 'html':
      return exportToHTML(terms);
    case 'json':
      return JSON.stringify(terms, null, 2);
    case 'csv':
      return exportToCSV(terms);
    default:
      return exportToMarkdown(terms);
  }
}

// Helper functions

function isValidTerm(term: string, minLength: number, maxLength: number): boolean {
  const cleaned = term.trim();
  return cleaned.length >= minLength && 
         cleaned.length <= maxLength && 
         /^[a-zA-Z]/.test(cleaned);
}

function isCommonWord(word: string): boolean {
  const commonWords = new Set([
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
    'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
    'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
    'those', 'what', 'which', 'who', 'when', 'where', 'why', 'how',
    'Chapter', 'Section', 'Figure', 'Table', 'Introduction', 'Conclusion',
    'First', 'Second', 'Third', 'Last', 'Next', 'Previous', 'Another',
    'Some', 'Many', 'Most', 'All', 'Each', 'Every', 'Other', 'Such'
  ]);
  
  return commonWords.has(word.toLowerCase());
}

function addTerm(
  terms: Map<string, GlossaryTerm>, 
  term: string, 
  chapterNumber: number, 
  context: string
): void {
  const key = term.toLowerCase();
  
  if (terms.has(key)) {
    const existing = terms.get(key)!;
    existing.frequency++;
    if (!existing.chapters.includes(chapterNumber)) {
      existing.chapters.push(chapterNumber);
    }
    existing.context.push(context);
  } else {
    terms.set(key, {
      term: term.trim(),
      frequency: 1,
      chapters: [chapterNumber],
      context: [context]
    });
  }
}

function extractDefinitionFromContext(term: string, contexts: string[]): string | undefined {
  // Simple pattern matching for definitions
  const definitionPatterns = [
    new RegExp(`${term}\\s+is\\s+([^.]+)`, 'i'),
    new RegExp(`${term}\\s+refers\\s+to\\s+([^.]+)`, 'i'),
    new RegExp(`${term}\\s*[:-]\\s*([^.]+)`, 'i')
  ];

  for (const context of contexts) {
    for (const pattern of definitionPatterns) {
      const match = context.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
  }

  return undefined;
}

function exportToMarkdown(terms: GlossaryTerm[]): string {
  let markdown = '# Glossary\n\n';
  
  terms.forEach(term => {
    markdown += `**${term.term}**: ${term.definition || 'Definition needed'}\n\n`;
  });
  
  return markdown;
}

function exportToHTML(terms: GlossaryTerm[]): string {
  let html = '<h1>Glossary</h1>\n<dl>\n';
  
  terms.forEach(term => {
    html += `  <dt>${term.term}</dt>\n`;
    html += `  <dd>${term.definition || 'Definition needed'}</dd>\n`;
  });
  
  html += '</dl>';
  return html;
}

function exportToCSV(terms: GlossaryTerm[]): string {
  let csv = 'Term,Definition,Frequency,Chapters\n';
  
  terms.forEach(term => {
    const definition = (term.definition || 'Definition needed').replace(/"/g, '""');
    const chapters = term.chapters.join(';');
    csv += `"${term.term}","${definition}",${term.frequency},"${chapters}"\n`;
  });
  
  return csv;
}
