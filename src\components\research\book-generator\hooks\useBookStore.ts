import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  BookState, 
  ChapterState, 
  BookMeta, 
  Appendix, 
  Citation,
  OutlineNode 
} from '../types';
import { 
  DEFAULT_BOOK_META, 
  DEFAULT_BOOK_SETTINGS 
} from '../constants';

interface BookStore extends BookState {
  // Actions
  initializeBook: () => void;
  resetBook: () => void;
  
  // Metadata actions
  updateMetadata: (meta: Partial<BookMeta>) => void;
  
  // Chapter actions
  addChapter: (title: string, outline?: OutlineNode[]) => string;
  updateChapter: (chapterId: string, updates: Partial<ChapterState>) => void;
  deleteChapter: (chapterId: string) => void;
  reorderChapters: (fromIndex: number, toIndex: number) => void;
  lockChapter: (chapterId: string) => void;
  unlockChapter: (chapterId: string) => void;
  
  // Chapter content actions
  saveDraft: (chapterId: string, content: string) => void;
  saveSummary: (chapterId: string, summary: string, glossaryTerms?: string[]) => void;
  updateChapterStatus: (chapterId: string, status: ChapterState['status']) => void;
  
  // Citation actions
  addCitation: (citation: Citation) => void;
  updateCitation: (citationId: string, updates: Partial<Citation>) => void;
  deleteCitation: (citationId: string) => void;
  linkCitationToChapter: (citationId: string, chapterNumber: number) => void;
  
  // Appendix actions
  addAppendix: (appendix: Omit<Appendix, 'id'>) => string;
  updateAppendix: (appendixId: string, updates: Partial<Appendix>) => void;
  deleteAppendix: (appendixId: string) => void;
  reorderAppendices: (fromIndex: number, toIndex: number) => void;
  
  // Settings actions
  updateGlobalSettings: (settings: Partial<BookState['globalSettings']>) => void;
  updateGenerationSettings: (settings: Partial<BookState['generationSettings']>) => void;
  
  // Utility getters
  getChapterById: (chapterId: string) => ChapterState | undefined;
  getChapterByNumber: (chapterNumber: number) => ChapterState | undefined;
  getNextChapterNumber: () => number;
  getTotalWordCount: () => number;
  getCompletedChapters: () => ChapterState[];
  getPendingChapters: () => ChapterState[];
  getAllCitations: () => Citation[];
  getChapterCitations: (chapterNumber: number) => Citation[];
}

const generateId = () => Math.random().toString(36).substr(2, 9);

export const useBookStore = create<BookStore>()(
  persist(
    (set, get) => ({
      // Initial state
      meta: { ...DEFAULT_BOOK_META },
      chapters: [],
      appendices: [],
      citationGraph: {},
      globalSettings: {
        citationStyle: 'apa',
        referencesPerChapter: false,
        includeIndex: true,
        includeGlossary: true,
      },
      generationSettings: {
        selectedModel: "google/gemini-2.5-flash-lite-preview-06-17",
        maxWordsPerChapter: DEFAULT_BOOK_SETTINGS.maxWordsPerChapter,
        tone: DEFAULT_BOOK_SETTINGS.defaultTone,
      },

      // Actions
      initializeBook: () => {
        set({
          meta: { ...DEFAULT_BOOK_META },
          chapters: [],
          appendices: [],
          citationGraph: {},
        });
      },

      resetBook: () => {
        set({
          meta: { ...DEFAULT_BOOK_META },
          chapters: [],
          appendices: [],
          citationGraph: {},
          globalSettings: {
            citationStyle: 'apa',
            referencesPerChapter: false,
            includeIndex: true,
            includeGlossary: true,
          },
          generationSettings: {
            selectedModel: "google/gemini-2.5-flash-lite-preview-06-17",
            maxWordsPerChapter: DEFAULT_BOOK_SETTINGS.maxWordsPerChapter,
            tone: DEFAULT_BOOK_SETTINGS.defaultTone,
          },
        });
      },

      updateMetadata: (meta) => {
        set((state) => ({
          meta: { ...state.meta, ...meta }
        }));
      },

      addChapter: (title, outline = []) => {
        const id = generateId();
        const chapterNumber = get().getNextChapterNumber();
        
        const newChapter: ChapterState = {
          id,
          title,
          chapterNumber,
          outline,
          draft: '',
          summary: null,
          glossaryTerms: [],
          citations: [],
          figures: [],
          status: 'pending',
          wordCount: 0,
          estimatedReadTime: 0,
        };

        set((state) => ({
          chapters: [...state.chapters, newChapter]
        }));

        return id;
      },

      updateChapter: (chapterId, updates) => {
        set((state) => ({
          chapters: state.chapters.map(chapter =>
            chapter.id === chapterId
              ? { ...chapter, ...updates }
              : chapter
          )
        }));
      },

      deleteChapter: (chapterId) => {
        set((state) => {
          const updatedChapters = state.chapters.filter(c => c.id !== chapterId);
          // Renumber remaining chapters
          const renumberedChapters = updatedChapters.map((chapter, index) => ({
            ...chapter,
            chapterNumber: index + 1
          }));
          
          return { chapters: renumberedChapters };
        });
      },

      reorderChapters: (fromIndex, toIndex) => {
        set((state) => {
          const chapters = [...state.chapters];
          const [movedChapter] = chapters.splice(fromIndex, 1);
          chapters.splice(toIndex, 0, movedChapter);
          
          // Renumber chapters
          const renumberedChapters = chapters.map((chapter, index) => ({
            ...chapter,
            chapterNumber: index + 1
          }));
          
          return { chapters: renumberedChapters };
        });
      },

      lockChapter: (chapterId) => {
        get().updateChapter(chapterId, { status: 'locked' });
      },

      unlockChapter: (chapterId) => {
        get().updateChapter(chapterId, { status: 'summarised' });
      },

      saveDraft: (chapterId, content) => {
        const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
        const estimatedReadTime = Math.ceil(wordCount / 200); // 200 words per minute
        
        get().updateChapter(chapterId, {
          draft: content,
          wordCount,
          estimatedReadTime,
          status: 'drafted'
        });
      },

      saveSummary: (chapterId, summary, glossaryTerms = []) => {
        get().updateChapter(chapterId, {
          summary,
          glossaryTerms,
          status: 'summarised'
        });
      },

      updateChapterStatus: (chapterId, status) => {
        get().updateChapter(chapterId, { status });
      },

      addCitation: (citation) => {
        set((state) => ({
          citationGraph: {
            ...state.citationGraph,
            [citation.id]: citation
          }
        }));
      },

      updateCitation: (citationId, updates) => {
        set((state) => ({
          citationGraph: {
            ...state.citationGraph,
            [citationId]: {
              ...state.citationGraph[citationId],
              ...updates
            }
          }
        }));
      },

      deleteCitation: (citationId) => {
        set((state) => {
          const { [citationId]: deleted, ...remaining } = state.citationGraph;
          return { citationGraph: remaining };
        });
      },

      linkCitationToChapter: (citationId, chapterNumber) => {
        const citation = get().citationGraph[citationId];
        if (citation && !citation.usedInChapters.includes(chapterNumber)) {
          get().updateCitation(citationId, {
            usedInChapters: [...citation.usedInChapters, chapterNumber]
          });
        }
      },

      addAppendix: (appendix) => {
        const id = generateId();
        const newAppendix: Appendix = { ...appendix, id };
        
        set((state) => ({
          appendices: [...state.appendices, newAppendix]
        }));
        
        return id;
      },

      updateAppendix: (appendixId, updates) => {
        set((state) => ({
          appendices: state.appendices.map(appendix =>
            appendix.id === appendixId
              ? { ...appendix, ...updates }
              : appendix
          )
        }));
      },

      deleteAppendix: (appendixId) => {
        set((state) => ({
          appendices: state.appendices.filter(a => a.id !== appendixId)
        }));
      },

      reorderAppendices: (fromIndex, toIndex) => {
        set((state) => {
          const appendices = [...state.appendices];
          const [movedAppendix] = appendices.splice(fromIndex, 1);
          appendices.splice(toIndex, 0, movedAppendix);
          
          // Update order property
          const reorderedAppendices = appendices.map((appendix, index) => ({
            ...appendix,
            order: index
          }));
          
          return { appendices: reorderedAppendices };
        });
      },

      updateGlobalSettings: (settings) => {
        set((state) => ({
          globalSettings: { ...state.globalSettings, ...settings }
        }));
      },

      updateGenerationSettings: (settings) => {
        set((state) => ({
          generationSettings: { ...state.generationSettings, ...settings }
        }));
      },

      // Utility getters
      getChapterById: (chapterId) => {
        return get().chapters.find(chapter => chapter.id === chapterId);
      },

      getChapterByNumber: (chapterNumber) => {
        return get().chapters.find(chapter => chapter.chapterNumber === chapterNumber);
      },

      getNextChapterNumber: () => {
        const chapters = get().chapters;
        return chapters.length > 0 
          ? Math.max(...chapters.map(c => c.chapterNumber)) + 1 
          : 1;
      },

      getTotalWordCount: () => {
        return get().chapters.reduce((total, chapter) => total + (chapter.wordCount || 0), 0);
      },

      getCompletedChapters: () => {
        return get().chapters.filter(chapter => chapter.status === 'locked');
      },

      getPendingChapters: () => {
        return get().chapters.filter(chapter => 
          chapter.status === 'pending' || chapter.status === 'generating'
        );
      },

      getAllCitations: () => {
        return Object.values(get().citationGraph);
      },

      getChapterCitations: (chapterNumber) => {
        return Object.values(get().citationGraph).filter(citation =>
          citation.usedInChapters.includes(chapterNumber)
        );
      },
    }),
    {
      name: 'pgp-book-v2', // Persist key
      partialize: (state) => ({
        meta: state.meta,
        chapters: state.chapters,
        appendices: state.appendices,
        citationGraph: state.citationGraph,
        globalSettings: state.globalSettings,
        generationSettings: state.generationSettings,
      }),
    }
  )
);
