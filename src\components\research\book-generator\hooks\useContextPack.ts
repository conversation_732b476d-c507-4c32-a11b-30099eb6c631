import { useMemo } from 'react';
import { useBookStore } from './useBookStore';
import { ContextPack, ChapterState } from '../types';
import { CONTEXT_SETTINGS } from '../constants';

/**
 * Hook for building context packs for chapter generation
 * Provides the previous chapter content and summaries of all earlier chapters
 */
export function useContextPack() {
  const { 
    chapters, 
    meta, 
    getChapterByNumber,
    getAllCitations 
  } = useBookStore();

  /**
   * Build context pack for a specific chapter
   */
  const buildContext = useMemo(() => {
    return (chapterIndex: number): ContextPack => {
      const currentChapterNumber = chapterIndex + 1;
      const totalChapters = chapters.length;

      // Get previous chapter (full content)
      let previousFull = '';
      if (chapterIndex > 0) {
        const previousChapter = getChapterByNumber(currentChapterNumber - 1);
        if (previousChapter?.draft) {
          previousFull = truncateContent(
            previousChapter.draft, 
            CONTEXT_SETTINGS.maxPreviousChapterWords
          );
        }
      }

      // Get summaries of all earlier chapters
      const priorSummaries: string[] = [];
      for (let i = 0; i < chapterIndex; i++) {
        const chapter = getChapterByNumber(i + 1);
        if (chapter?.summary) {
          priorSummaries.push(chapter.summary);
        }
      }

      // Collect glossary terms from previous chapters
      const glossaryTerms: string[] = [];
      if (CONTEXT_SETTINGS.includeGlossaryTerms) {
        for (let i = 0; i < chapterIndex; i++) {
          const chapter = getChapterByNumber(i + 1);
          if (chapter?.glossaryTerms) {
            glossaryTerms.push(...chapter.glossaryTerms);
          }
        }
      }

      return {
        previousFull,
        priorSummaries,
        bookMeta: meta,
        currentChapterNumber,
        totalChapters,
        glossaryTerms: [...new Set(glossaryTerms)], // Remove duplicates
      };
    };
  }, [chapters, meta, getChapterByNumber]);

  /**
   * Get context summary for display purposes
   */
  const getContextSummary = useMemo(() => {
    return (chapterIndex: number) => {
      const context = buildContext(chapterIndex);
      
      const previousWordCount = context.previousFull 
        ? context.previousFull.split(/\s+/).length 
        : 0;
      
      const summariesWordCount = context.priorSummaries
        .join(' ')
        .split(/\s+/).length;

      const totalContextWords = previousWordCount + summariesWordCount;

      return {
        hasPreviousChapter: !!context.previousFull,
        previousChapterWords: previousWordCount,
        priorSummariesCount: context.priorSummaries.length,
        summariesWordCount,
        totalContextWords,
        glossaryTermsCount: context.glossaryTerms.length,
        isWithinLimits: totalContextWords <= CONTEXT_SETTINGS.maxTotalContextWords,
      };
    };
  }, [buildContext]);

  /**
   * Validate if context is ready for generation
   */
  const validateContext = useMemo(() => {
    return (chapterIndex: number): { isValid: boolean; issues: string[] } => {
      const issues: string[] = [];
      
      if (chapterIndex === 0) {
        // First chapter doesn't need context validation
        return { isValid: true, issues: [] };
      }

      const context = buildContext(chapterIndex);
      const summary = getContextSummary(chapterIndex);

      // Check if previous chapter exists and is completed
      const previousChapter = getChapterByNumber(chapterIndex);
      if (!previousChapter || previousChapter.status === 'pending') {
        issues.push('Previous chapter must be completed first');
      }

      // Check if previous chapter has a summary (for chapters after the second)
      if (chapterIndex > 1) {
        const chapterBeforePrevious = getChapterByNumber(chapterIndex - 1);
        if (!chapterBeforePrevious?.summary) {
          issues.push('Previous chapters must have summaries');
        }
      }

      // Check context size limits
      if (!summary.isWithinLimits) {
        issues.push(`Context too large (${summary.totalContextWords} words, max ${CONTEXT_SETTINGS.maxTotalContextWords})`);
      }

      return {
        isValid: issues.length === 0,
        issues
      };
    };
  }, [buildContext, getContextSummary, getChapterByNumber]);

  /**
   * Get formatted context string for AI prompt
   */
  const getFormattedContext = useMemo(() => {
    return (chapterIndex: number): string => {
      const context = buildContext(chapterIndex);
      
      if (chapterIndex === 0) {
        return `This is the first chapter of "${context.bookMeta.title}".`;
      }

      let formattedContext = '';

      // Add book context
      formattedContext += `Book: "${context.bookMeta.title}"\n`;
      formattedContext += `Chapter ${context.currentChapterNumber} of ${context.totalChapters}\n\n`;

      // Add previous chapter content
      if (context.previousFull) {
        formattedContext += `PREVIOUS CHAPTER CONTENT:\n`;
        formattedContext += `${context.previousFull}\n\n`;
      }

      // Add prior summaries
      if (context.priorSummaries.length > 0) {
        formattedContext += `SUMMARIES OF EARLIER CHAPTERS:\n`;
        context.priorSummaries.forEach((summary, index) => {
          formattedContext += `Chapter ${index + 1} Summary:\n${summary}\n\n`;
        });
      }

      // Add glossary terms
      if (context.glossaryTerms.length > 0) {
        formattedContext += `KEY TERMS FROM PREVIOUS CHAPTERS:\n`;
        formattedContext += context.glossaryTerms.join(', ') + '\n\n';
      }

      return formattedContext;
    };
  }, [buildContext]);

  return {
    buildContext,
    getContextSummary,
    validateContext,
    getFormattedContext,
  };
}

/**
 * Utility function to truncate content to a maximum word count
 */
function truncateContent(content: string, maxWords: number): string {
  const words = content.split(/\s+/);
  if (words.length <= maxWords) {
    return content;
  }
  
  return words.slice(0, maxWords).join(' ') + '\n\n[Content truncated for context...]';
}

/**
 * Hook for getting context statistics across all chapters
 */
export function useContextStats() {
  const { chapters } = useBookStore();
  const { getContextSummary } = useContextPack();

  const stats = useMemo(() => {
    const chapterStats = chapters.map((_, index) => ({
      chapterNumber: index + 1,
      ...getContextSummary(index)
    }));

    const totalContextWords = chapterStats.reduce(
      (sum, stat) => sum + stat.totalContextWords, 
      0
    );

    const chaptersWithContext = chapterStats.filter(
      stat => stat.hasPreviousChapter
    ).length;

    const chaptersOverLimit = chapterStats.filter(
      stat => !stat.isWithinLimits
    ).length;

    return {
      chapterStats,
      totalContextWords,
      chaptersWithContext,
      chaptersOverLimit,
      averageContextWords: chaptersWithContext > 0 
        ? Math.round(totalContextWords / chaptersWithContext) 
        : 0,
    };
  }, [chapters, getContextSummary]);

  return stats;
}
