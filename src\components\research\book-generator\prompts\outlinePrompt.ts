import { BookMeta } from '../types';

/**
 * Generate a prompt for creating a book outline
 */
export function createOutlinePrompt(
  bookMeta: BookMeta,
  chapterCount: number = 8,
  detailLevel: 'basic' | 'detailed' = 'detailed'
): string {
  const prompt = `You are an expert book outline creator. Generate a comprehensive outline for the following book:

BOOK INFORMATION:
Title: "${bookMeta.title}"
${bookMeta.subtitle ? `Subtitle: "${bookMeta.subtitle}"` : ''}
Authors: <AUTHORS>
Genre: ${bookMeta.genre}
${bookMeta.description ? `Description: ${bookMeta.description}` : ''}
${bookMeta.targetAudience ? `Target Audience: ${bookMeta.targetAudience}` : ''}
Keywords: ${bookMeta.keywords.join(', ')}

REQUIREMENTS:
- Create ${chapterCount} chapters
- Each chapter should have ${detailLevel === 'detailed' ? '≥10' : '5-8'} sub-headings
- Use hierarchical numbering (1, 1.1, 1.1.1, etc.)
- Provide estimated word counts for each section
- Include brief descriptions for each section
- Ensure logical flow and progression between chapters
- Make the outline comprehensive and well-structured

FORMAT:
For each chapter, provide:
1. Chapter number and title
2. Brief chapter description
3. Detailed sub-sections with numbering
4. Estimated word count for each section

Example format:
Chapter 1: Introduction and Overview
Description: Setting the foundation and context for the book
Estimated words: 2000

1.1 Background and Context (400 words)
- Brief description of what this section covers

1.2 Problem Statement (500 words)
- Brief description of what this section covers

1.3 Objectives and Scope (600 words)
- Brief description of what this section covers

1.4 Book Structure Overview (500 words)
- Brief description of what this section covers

Please generate the complete outline now:`;

  return prompt;
}

/**
 * Generate a prompt for refining an existing outline
 */
export function createOutlineRefinementPrompt(
  bookMeta: BookMeta,
  currentOutline: string,
  feedback: string
): string {
  return `You are refining a book outline based on feedback. Here's the current outline and feedback:

BOOK INFORMATION:
Title: "${bookMeta.title}"
Genre: ${bookMeta.genre}
Target Audience: ${bookMeta.targetAudience || 'General'}

CURRENT OUTLINE:
${currentOutline}

FEEDBACK:
${feedback}

Please revise the outline to address the feedback while maintaining:
- Logical chapter progression
- Appropriate depth and detail
- Consistent formatting and numbering
- Realistic word count estimates

Provide the revised outline:`;
}

/**
 * Generate a prompt for creating chapter-specific outlines
 */
export function createChapterOutlinePrompt(
  bookMeta: BookMeta,
  chapterNumber: number,
  chapterTitle: string,
  chapterDescription?: string,
  previousChapters?: string[]
): string {
  let prompt = `You are creating a detailed outline for a specific chapter. Generate a comprehensive structure for:

BOOK CONTEXT:
Title: "${bookMeta.title}"
Genre: ${bookMeta.genre}

CHAPTER TO OUTLINE:
Chapter ${chapterNumber}: "${chapterTitle}"
${chapterDescription ? `Description: ${chapterDescription}` : ''}

`;

  if (previousChapters && previousChapters.length > 0) {
    prompt += `PREVIOUS CHAPTERS:
${previousChapters.map((title, index) => `Chapter ${index + 1}: ${title}`).join('\n')}

`;
  }

  prompt += `REQUIREMENTS:
- Create 8-12 detailed sub-sections
- Use hierarchical numbering (${chapterNumber}.1, ${chapterNumber}.1.1, etc.)
- Provide estimated word counts (aim for 2000-3000 total words)
- Include brief descriptions for each sub-section
- Ensure smooth flow and logical progression
- Consider how this chapter connects to previous and future chapters

FORMAT:
${chapterNumber}.1 Section Title (estimated words)
- Brief description of content and purpose

${chapterNumber}.1.1 Subsection Title (estimated words)
- Brief description

Generate the detailed chapter outline now:`;

  return prompt;
}

/**
 * Generate a prompt for outline validation and improvement
 */
export function createOutlineValidationPrompt(
  bookMeta: BookMeta,
  outline: string
): string {
  return `You are validating and improving a book outline. Analyze the following outline and provide feedback:

BOOK INFORMATION:
Title: "${bookMeta.title}"
Genre: ${bookMeta.genre}
Target Audience: ${bookMeta.targetAudience || 'General'}

OUTLINE TO VALIDATE:
${outline}

Please analyze and provide:

1. STRENGTHS:
- What works well in this outline
- Strong structural elements
- Good chapter progression

2. AREAS FOR IMPROVEMENT:
- Missing topics or gaps
- Structural issues
- Balance problems between chapters
- Word count distribution issues

3. SPECIFIC RECOMMENDATIONS:
- Concrete suggestions for improvement
- Additional sections to consider
- Reorganization suggestions

4. OVERALL ASSESSMENT:
- Is the outline comprehensive?
- Does it serve the target audience?
- Is the progression logical?

Provide your analysis:`;
}

/**
 * Generate outline templates based on genre
 */
export function getGenreOutlineTemplate(genre: string): string[] {
  const templates: Record<string, string[]> = {
    academic: [
      'Introduction and Literature Review',
      'Theoretical Framework',
      'Methodology',
      'Analysis and Findings',
      'Discussion and Implications',
      'Conclusion and Future Research'
    ],
    business: [
      'Executive Summary and Overview',
      'Market Analysis and Opportunity',
      'Strategic Framework',
      'Implementation Strategy',
      'Risk Management and Mitigation',
      'Performance Metrics and ROI',
      'Future Outlook and Recommendations'
    ],
    technical: [
      'Introduction and Prerequisites',
      'Fundamental Concepts',
      'Core Technologies and Tools',
      'Advanced Techniques',
      'Best Practices and Patterns',
      'Troubleshooting and Optimization',
      'Case Studies and Applications',
      'Future Trends and Conclusion'
    ],
    'self-help': [
      'Understanding the Problem',
      'The Foundation for Change',
      'Core Principles and Strategies',
      'Practical Implementation',
      'Overcoming Obstacles',
      'Maintaining Progress',
      'Advanced Techniques',
      'Living the Transformation'
    ],
    biography: [
      'Early Life and Background',
      'Formative Years and Education',
      'Career Beginnings',
      'Major Achievements and Milestones',
      'Challenges and Setbacks',
      'Personal Life and Relationships',
      'Legacy and Impact',
      'Reflections and Lessons'
    ],
    history: [
      'Historical Context and Background',
      'Key Events and Timeline',
      'Major Figures and Personalities',
      'Social and Cultural Impact',
      'Political and Economic Factors',
      'Consequences and Aftermath',
      'Historical Significance',
      'Lessons for Today'
    ]
  };

  return templates[genre] || templates.business;
}
