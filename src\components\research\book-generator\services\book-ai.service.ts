import { 
  ChapterGenerationRequest, 
  SummaryGenerationRequest,
  GenerationResponse,
  SummaryResponse,
  ContextPack 
} from '../types';

interface AIGenerationOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

class BookAIService {
  private apiKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found. AI generation will not work.');
    }
  }

  /**
   * Generate a complete chapter based on outline and context
   */
  async generateChapter(request: ChapterGenerationRequest): Promise<GenerationResponse> {
    try {
      const prompt = this.buildChapterPrompt(request);
      
      const response = await this.callAI(prompt, {
        model: request.generationSettings.selectedModel,
        maxTokens: Math.min(request.generationSettings.maxWordsPerChapter * 1.5, 8192),
        temperature: 0.7
      });

      // Extract citations from the generated content
      const citations = this.extractCitations(response);
      
      // Count words
      const wordCount = this.countWords(response);

      return {
        content: response,
        wordCount,
        citations,
        success: true
      };
    } catch (error) {
      console.error('Chapter generation failed:', error);
      return {
        content: '',
        wordCount: 0,
        citations: [],
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate a summary of a chapter
   */
  async generateSummary(request: SummaryGenerationRequest): Promise<SummaryResponse> {
    try {
      const prompt = this.buildSummaryPrompt(request);
      
      const response = await this.callAI(prompt, {
        maxTokens: 600,
        temperature: 0.5
      });

      // Parse the response to extract summary and glossary terms
      const parsed = this.parseSummaryResponse(response);

      return {
        summary: parsed.summary,
        glossaryTerms: parsed.glossaryTerms,
        keyPoints: parsed.keyPoints,
        wordCount: this.countWords(parsed.summary),
        success: true
      };
    } catch (error) {
      console.error('Summary generation failed:', error);
      return {
        summary: '',
        glossaryTerms: [],
        keyPoints: [],
        wordCount: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate an outline for a book based on metadata
   */
  async generateOutline(
    title: string, 
    description: string, 
    genre: string, 
    keywords: string[]
  ): Promise<{ success: boolean; outline?: any; error?: string }> {
    try {
      const prompt = `Generate a detailed book outline for:
Title: "${title}"
Genre: ${genre}
Description: ${description}
Keywords: ${keywords.join(', ')}

Create 6-8 chapters with:
- Descriptive chapter titles
- 3-5 subsections per chapter
- Estimated word counts
- Brief descriptions

Format as a structured outline with numbering (1, 1.1, 1.1.1, etc.)`;

      const response = await this.callAI(prompt, {
        maxTokens: 2048,
        temperature: 0.8
      });

      return {
        success: true,
        outline: response
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Build the prompt for chapter generation
   */
  private buildChapterPrompt(request: ChapterGenerationRequest): string {
    const { chapterTitle, chapterNumber, outline, contextPack, generationSettings } = request;
    
    let prompt = `You are an expert author writing a ${generationSettings.tone} book. `;
    
    // Add context if available
    if (contextPack.currentChapterNumber > 1) {
      prompt += `\n\nCONTEXT FROM PREVIOUS CHAPTERS:\n`;
      
      if (contextPack.previousFull) {
        prompt += `\nPrevious Chapter Content:\n${contextPack.previousFull}\n`;
      }
      
      if (contextPack.priorSummaries.length > 0) {
        prompt += `\nSummaries of Earlier Chapters:\n`;
        contextPack.priorSummaries.forEach((summary, index) => {
          prompt += `Chapter ${index + 1}: ${summary}\n`;
        });
      }
      
      if (contextPack.glossaryTerms.length > 0) {
        prompt += `\nKey Terms from Previous Chapters: ${contextPack.glossaryTerms.join(', ')}\n`;
      }
    }

    prompt += `\n\nBOOK INFORMATION:
Title: "${contextPack.bookMeta.title}"
${contextPack.bookMeta.subtitle ? `Subtitle: "${contextPack.bookMeta.subtitle}"` : ''}
Genre: ${contextPack.bookMeta.genre}
Authors: <AUTHORS>
${contextPack.bookMeta.description ? `Description: ${contextPack.bookMeta.description}` : ''}

CHAPTER TO WRITE:
Chapter ${chapterNumber}: "${chapterTitle}"
This is chapter ${chapterNumber} of ${contextPack.totalChapters} total chapters.

CHAPTER OUTLINE:`;

    if (outline.length > 0) {
      outline.forEach(section => {
        prompt += `\n${section.numbering}. ${section.title}`;
        if (section.description) {
          prompt += ` - ${section.description}`;
        }
        if (section.children) {
          section.children.forEach(subsection => {
            prompt += `\n  ${subsection.numbering}. ${subsection.title}`;
            if (subsection.description) {
              prompt += ` - ${subsection.description}`;
            }
          });
        }
      });
    }

    prompt += `\n\nINSTRUCTIONS:
1. Write a complete chapter of approximately ${generationSettings.maxWordsPerChapter} words
2. Use a ${generationSettings.tone} tone throughout
3. Follow the provided outline structure
4. Include relevant citations in (Author, Year) format where appropriate
5. Ensure smooth transitions between sections
6. Build upon the context from previous chapters
7. Use clear headings and subheadings
8. Write in markdown format
9. Do not include the chapter title as a heading (it will be added separately)

Begin writing the chapter content now:`;

    return prompt;
  }

  /**
   * Build the prompt for summary generation
   */
  private buildSummaryPrompt(request: SummaryGenerationRequest): string {
    return `Analyze the following chapter and create a comprehensive summary:

CHAPTER: "${request.chapterTitle}" (Chapter ${request.chapterNumber})

CONTENT:
${request.chapterContent}

Please provide:
1. A concise summary (maximum ${request.maxWords} words)
2. 5-10 key terms for the glossary
3. 3-5 main points or takeaways

Format your response as:

SUMMARY:
[Your summary here]

GLOSSARY TERMS:
- Term 1: Brief definition
- Term 2: Brief definition
[etc.]

KEY POINTS:
- Point 1
- Point 2
[etc.]`;
  }

  /**
   * Make API call to AI service
   */
  private async callAI(prompt: string, options: AIGenerationOptions = {}): Promise<string> {
    const { 
      model = "google/gemini-2.5-flash-lite-preview-06-17", 
      maxTokens = 2048,
      temperature = 0.7 
    } = options;

    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: "system",
            content: "You are an expert author and writing assistant. Create high-quality, well-structured content that flows naturally and maintains consistency with the provided context."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: maxTokens,
        temperature
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  /**
   * Extract citations from generated content
   */
  private extractCitations(content: string): string[] {
    const citationRegex = /\(([^)]+?,\s*\d{4}[^)]*)\)/g;
    const matches = [...content.matchAll(citationRegex)];
    return [...new Set(matches.map(match => match[0]))];
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Parse summary response to extract components
   */
  private parseSummaryResponse(response: string): {
    summary: string;
    glossaryTerms: string[];
    keyPoints: string[];
  } {
    const sections = response.split(/(?:SUMMARY:|GLOSSARY TERMS:|KEY POINTS:)/i);
    
    let summary = '';
    let glossaryTerms: string[] = [];
    let keyPoints: string[] = [];

    if (sections.length >= 2) {
      summary = sections[1].trim();
    }

    if (sections.length >= 3) {
      const glossarySection = sections[2];
      glossaryTerms = glossarySection
        .split('\n')
        .filter(line => line.trim().startsWith('-'))
        .map(line => line.replace(/^-\s*/, '').trim())
        .filter(term => term.length > 0);
    }

    if (sections.length >= 4) {
      const keyPointsSection = sections[3];
      keyPoints = keyPointsSection
        .split('\n')
        .filter(line => line.trim().startsWith('-'))
        .map(line => line.replace(/^-\s*/, '').trim())
        .filter(point => point.length > 0);
    }

    return { summary, glossaryTerms, keyPoints };
  }
}

export default new BookAIService();
