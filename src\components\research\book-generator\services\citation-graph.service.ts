import { Citation } from '../types';

class CitationGraphService {
  /**
   * Extract citations from text content
   */
  extractCitationsFromText(text: string): { citations: Citation[]; matches: string[] } {
    // Regular expression to match citations in various formats:
    // (<PERSON>, 2023)
    // (<PERSON> et al., 2023)
    // (<PERSON> and <PERSON>, 2023)
    // (<PERSON> & <PERSON>, 2023)
    // (<PERSON>, <PERSON>, & <PERSON>, 2023)
    // (Smith, 2023; Jones, 2022)
    const citationRegex = /\(([^)]+?,\s*\d{4}[^)]*)\)/g;
    
    const matches = [...text.matchAll(citationRegex)].map(match => match[0]);
    const uniqueMatches = Array.from(new Set(matches));
    
    const citations: Citation[] = [];
    
    uniqueMatches.forEach(match => {
      const citationText = match.slice(1, -1); // Remove parentheses
      const parts = citationText.split(';').map(part => part.trim());
      
      parts.forEach(part => {
        const citation = this.parseCitationString(part);
        if (citation) {
          citations.push(citation);
        }
      });
    });
    
    return { citations, matches: uniqueMatches };
  }

  /**
   * Parse a single citation string into a Citation object
   */
  private parseCitationString(citationStr: string): Citation | null {
    // Match patterns like "Smith, 2023" or "Smith et al., 2023"
    const match = citationStr.match(/^(.+?),\s*(\d{4})(?:\s*,\s*(.+))?$/);
    
    if (!match) return null;
    
    const [, authorPart, yearStr, extraPart] = match;
    const year = parseInt(yearStr);
    
    // Parse authors
    let authors: string[] = [];
    if (authorPart.includes(' et al.')) {
      const mainAuthor = authorPart.replace(' et al.', '').trim();
      authors = [mainAuthor];
    } else if (authorPart.includes(' and ') || authorPart.includes(' & ')) {
      authors = authorPart
        .split(/ and | & /)
        .map(author => author.trim())
        .filter(author => author.length > 0);
    } else {
      authors = [authorPart.trim()];
    }
    
    const id = this.generateCitationId(authors[0], year);
    const key = `(${citationStr})`;
    
    return {
      id,
      key,
      title: '', // Will be filled in later
      authors,
      year,
      type: 'article', // Default type
      usedInChapters: []
    };
  }

  /**
   * Generate a unique ID for a citation
   */
  private generateCitationId(firstAuthor: string, year: number): string {
    const cleanAuthor = firstAuthor.replace(/[^a-zA-Z]/g, '').toLowerCase();
    return `${cleanAuthor}${year}`;
  }

  /**
   * Generate a full reference from a citation
   */
  generateReference(citation: Citation): string {
    const { authors, year, title, journal, volume, pages, doi, url, type } = citation;
    
    let reference = '';
    
    // Authors
    if (authors.length === 1) {
      reference += authors[0];
    } else if (authors.length === 2) {
      reference += `${authors[0]} & ${authors[1]}`;
    } else if (authors.length > 2) {
      reference += `${authors[0]} et al.`;
    }
    
    // Year
    reference += ` (${year}).`;
    
    // Title
    if (title) {
      if (type === 'article') {
        reference += ` ${title}.`;
      } else if (type === 'book') {
        reference += ` *${title}*.`;
      } else {
        reference += ` ${title}.`;
      }
    }
    
    // Journal/Publication
    if (journal) {
      reference += ` *${journal}*`;
      if (volume) {
        reference += `, ${volume}`;
        if (pages) {
          reference += `, ${pages}`;
        }
      }
      reference += '.';
    }
    
    // DOI or URL
    if (doi) {
      reference += ` https://doi.org/${doi}`;
    } else if (url) {
      reference += ` ${url}`;
    }
    
    return reference;
  }

  /**
   * Merge duplicate citations
   */
  mergeCitations(citations: Citation[]): Citation[] {
    const citationMap = new Map<string, Citation>();
    
    citations.forEach(citation => {
      const existingCitation = citationMap.get(citation.id);
      
      if (existingCitation) {
        // Merge chapter usage
        const combinedChapters = [
          ...new Set([...existingCitation.usedInChapters, ...citation.usedInChapters])
        ];
        
        // Keep the citation with more complete information
        const mergedCitation: Citation = {
          ...existingCitation,
          usedInChapters: combinedChapters,
          title: existingCitation.title || citation.title,
          journal: existingCitation.journal || citation.journal,
          volume: existingCitation.volume || citation.volume,
          pages: existingCitation.pages || citation.pages,
          doi: existingCitation.doi || citation.doi,
          url: existingCitation.url || citation.url
        };
        
        citationMap.set(citation.id, mergedCitation);
      } else {
        citationMap.set(citation.id, { ...citation });
      }
    });
    
    return Array.from(citationMap.values());
  }

  /**
   * Validate citation format
   */
  validateCitation(citation: Citation): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];
    
    if (!citation.authors || citation.authors.length === 0) {
      issues.push('Citation must have at least one author');
    }
    
    if (!citation.year || citation.year < 1800 || citation.year > new Date().getFullYear() + 5) {
      issues.push('Citation must have a valid year');
    }
    
    if (citation.authors.some(author => author.trim().length < 2)) {
      issues.push('Author names must be at least 2 characters long');
    }
    
    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Sort citations alphabetically by first author
   */
  sortCitations(citations: Citation[]): Citation[] {
    return [...citations].sort((a, b) => {
      const authorA = a.authors[0]?.toLowerCase() || '';
      const authorB = b.authors[0]?.toLowerCase() || '';
      
      if (authorA !== authorB) {
        return authorA.localeCompare(authorB);
      }
      
      // If same author, sort by year (newest first)
      return b.year - a.year;
    });
  }

  /**
   * Filter citations by chapter
   */
  getCitationsForChapter(citations: Citation[], chapterNumber: number): Citation[] {
    return citations.filter(citation => 
      citation.usedInChapters.includes(chapterNumber)
    );
  }

  /**
   * Get citation statistics
   */
  getCitationStats(citations: Citation[]) {
    const totalCitations = citations.length;
    const citationsByType = citations.reduce((acc, citation) => {
      acc[citation.type] = (acc[citation.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const citationsByYear = citations.reduce((acc, citation) => {
      acc[citation.year] = (acc[citation.year] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
    
    const yearRange = citations.length > 0 ? {
      earliest: Math.min(...citations.map(c => c.year)),
      latest: Math.max(...citations.map(c => c.year))
    } : null;
    
    const chaptersWithCitations = new Set(
      citations.flatMap(c => c.usedInChapters)
    ).size;
    
    return {
      totalCitations,
      citationsByType,
      citationsByYear,
      yearRange,
      chaptersWithCitations
    };
  }

  /**
   * Create a bibliography from citations
   */
  createBibliography(citations: Citation[], style: 'apa' | 'mla' | 'chicago' | 'ieee' = 'apa'): string[] {
    const sortedCitations = this.sortCitations(citations);
    
    return sortedCitations.map(citation => {
      switch (style) {
        case 'apa':
          return this.generateAPAReference(citation);
        case 'mla':
          return this.generateMLAReference(citation);
        case 'chicago':
          return this.generateChicagoReference(citation);
        case 'ieee':
          return this.generateIEEEReference(citation);
        default:
          return this.generateReference(citation);
      }
    });
  }

  /**
   * Generate APA style reference
   */
  private generateAPAReference(citation: Citation): string {
    // This is a simplified APA format
    return this.generateReference(citation);
  }

  /**
   * Generate MLA style reference
   */
  private generateMLAReference(citation: Citation): string {
    const { authors, title, journal, year, pages } = citation;
    
    let reference = '';
    
    // Author (Last, First)
    if (authors.length > 0) {
      reference += authors[0];
    }
    
    // Title
    if (title) {
      reference += `. "${title}."`;
    }
    
    // Journal
    if (journal) {
      reference += ` *${journal}*`;
    }
    
    // Year and pages
    if (year) {
      reference += `, ${year}`;
      if (pages) {
        reference += `, pp. ${pages}`;
      }
    }
    
    reference += '.';
    
    return reference;
  }

  /**
   * Generate Chicago style reference
   */
  private generateChicagoReference(citation: Citation): string {
    // Simplified Chicago format
    return this.generateReference(citation);
  }

  /**
   * Generate IEEE style reference
   */
  private generateIEEEReference(citation: Citation): string {
    // Simplified IEEE format
    return this.generateReference(citation);
  }
}

export default new CitationGraphService();
