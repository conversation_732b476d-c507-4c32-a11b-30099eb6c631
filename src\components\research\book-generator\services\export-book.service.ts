import { BookExportData, ExportOptions, BookState } from '../types';
import citationGraphService from './citation-graph.service';

class ExportBookService {
  /**
   * Export book to PDF format
   */
  async exportToPDF(bookState: BookState, options: ExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(bookState, options);
      const content = this.generatePDFContent(exportData);
      
      // For now, we'll create a simple text file
      // In a real implementation, this would use a PDF generation library
      const blob = new Blob([content], { type: 'text/plain' });
      this.downloadFile(blob, `${bookState.meta.title}.pdf`, 'application/pdf');
      
    } catch (error) {
      console.error('PDF export failed:', error);
      throw new Error('Failed to export PDF');
    }
  }

  /**
   * Export book to EPUB format
   */
  async exportToEPUB(bookState: BookState, options: ExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(bookState, options);
      const content = this.generateEPUBContent(exportData);
      
      // For now, we'll create a simple HTML file
      // In a real implementation, this would create a proper EPUB package
      const blob = new Blob([content], { type: 'text/html' });
      this.downloadFile(blob, `${bookState.meta.title}.epub`, 'application/epub+zip');
      
    } catch (error) {
      console.error('EPUB export failed:', error);
      throw new Error('Failed to export EPUB');
    }
  }

  /**
   * Export book to LaTeX format
   */
  async exportToLaTeX(bookState: BookState, options: ExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(bookState, options);
      const content = this.generateLaTeXContent(exportData);
      
      const blob = new Blob([content], { type: 'text/plain' });
      this.downloadFile(blob, `${bookState.meta.title}.tex`, 'application/x-latex');
      
    } catch (error) {
      console.error('LaTeX export failed:', error);
      throw new Error('Failed to export LaTeX');
    }
  }

  /**
   * Export book to Word document format
   */
  async exportToDocx(bookState: BookState, options: ExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(bookState, options);
      const content = this.generateDocxContent(exportData);
      
      // For now, we'll create a simple text file
      // In a real implementation, this would use a DOCX generation library
      const blob = new Blob([content], { type: 'text/plain' });
      this.downloadFile(blob, `${bookState.meta.title}.docx`, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
      
    } catch (error) {
      console.error('DOCX export failed:', error);
      throw new Error('Failed to export DOCX');
    }
  }

  /**
   * Prepare export data from book state
   */
  private prepareExportData(bookState: BookState, options: ExportOptions): BookExportData {
    // Sort chapters by number
    const sortedChapters = [...bookState.chapters].sort((a, b) => a.chapterNumber - b.chapterNumber);
    
    // Get all citations
    const citations = Object.values(bookState.citationGraph);
    
    // Get all figures
    const figures = sortedChapters.flatMap(chapter => chapter.figures || []);
    
    // Create table of contents from chapter outlines
    const tableOfContents = sortedChapters.map(chapter => ({
      id: chapter.id,
      title: chapter.title,
      level: 1,
      numbering: chapter.chapterNumber.toString(),
      children: chapter.outline || []
    }));

    return {
      meta: bookState.meta,
      chapters: sortedChapters,
      appendices: bookState.appendices,
      citations,
      figures,
      tableOfContents,
      exportOptions: options
    };
  }

  /**
   * Generate PDF content (simplified)
   */
  private generatePDFContent(data: BookExportData): string {
    let content = '';

    // Title page
    content += `${data.meta.title}\n`;
    if (data.meta.subtitle) {
      content += `${data.meta.subtitle}\n`;
    }
    content += `\nBy: ${data.meta.authors.join(', ')}\n\n`;
    content += '='.repeat(50) + '\n\n';

    // Table of contents
    if (data.exportOptions.includeTableOfContents) {
      content += 'TABLE OF CONTENTS\n\n';
      data.chapters.forEach(chapter => {
        content += `Chapter ${chapter.chapterNumber}: ${chapter.title}\n`;
      });
      content += '\n' + '='.repeat(50) + '\n\n';
    }

    // Chapters
    data.chapters.forEach(chapter => {
      if (data.exportOptions.chapterBreaks) {
        content += '\n' + '='.repeat(50) + '\n';
      }
      content += `CHAPTER ${chapter.chapterNumber}: ${chapter.title.toUpperCase()}\n\n`;
      content += chapter.draft + '\n\n';
    });

    // Appendices
    if (data.appendices.length > 0) {
      content += '\n' + '='.repeat(50) + '\n';
      content += 'APPENDICES\n\n';
      data.appendices.forEach(appendix => {
        content += `${appendix.title}\n`;
        content += '-'.repeat(appendix.title.length) + '\n';
        content += appendix.content + '\n\n';
      });
    }

    // References
    if (data.exportOptions.includeReferences && data.citations.length > 0) {
      content += '\n' + '='.repeat(50) + '\n';
      content += 'REFERENCES\n\n';
      const bibliography = citationGraphService.createBibliography(data.citations);
      content += bibliography.join('\n\n');
    }

    return content;
  }

  /**
   * Generate EPUB content (simplified HTML)
   */
  private generateEPUBContent(data: BookExportData): string {
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.meta.title}</title>
    <style>
        body { font-family: Georgia, serif; line-height: 1.6; margin: 2em; }
        h1 { color: #333; border-bottom: 2px solid #333; }
        h2 { color: #666; margin-top: 2em; }
        .chapter { page-break-before: always; }
        .toc { margin: 2em 0; }
        .toc ul { list-style-type: none; }
        .references { margin-top: 3em; }
    </style>
</head>
<body>`;

    // Title page
    html += `<div class="title-page">
        <h1>${data.meta.title}</h1>`;
    if (data.meta.subtitle) {
      html += `<h2>${data.meta.subtitle}</h2>`;
    }
    html += `<p><strong>By: ${data.meta.authors.join(', ')}</strong></p>
    </div>`;

    // Table of contents
    if (data.exportOptions.includeTableOfContents) {
      html += `<div class="toc">
        <h2>Table of Contents</h2>
        <ul>`;
      data.chapters.forEach(chapter => {
        html += `<li>Chapter ${chapter.chapterNumber}: ${chapter.title}</li>`;
      });
      html += `</ul></div>`;
    }

    // Chapters
    data.chapters.forEach(chapter => {
      html += `<div class="chapter">
        <h1>Chapter ${chapter.chapterNumber}: ${chapter.title}</h1>
        <div>${this.markdownToHTML(chapter.draft)}</div>
      </div>`;
    });

    // Appendices
    if (data.appendices.length > 0) {
      html += `<div class="appendices">
        <h1>Appendices</h1>`;
      data.appendices.forEach(appendix => {
        html += `<div class="appendix">
          <h2>${appendix.title}</h2>
          <div>${this.markdownToHTML(appendix.content)}</div>
        </div>`;
      });
      html += `</div>`;
    }

    // References
    if (data.exportOptions.includeReferences && data.citations.length > 0) {
      html += `<div class="references">
        <h1>References</h1>`;
      const bibliography = citationGraphService.createBibliography(data.citations);
      bibliography.forEach(ref => {
        html += `<p>${ref}</p>`;
      });
      html += `</div>`;
    }

    html += `</body></html>`;
    return html;
  }

  /**
   * Generate LaTeX content
   */
  private generateLaTeXContent(data: BookExportData): string {
    let latex = `\\documentclass[12pt]{book}
\\usepackage[utf8]{inputenc}
\\usepackage{geometry}
\\usepackage{fancyhdr}
\\usepackage{hyperref}

\\title{${this.escapeLaTeX(data.meta.title)}}
\\author{${data.meta.authors.map(a => this.escapeLaTeX(a)).join(' \\and ')}}
\\date{\\today}

\\begin{document}

\\maketitle

`;

    // Table of contents
    if (data.exportOptions.includeTableOfContents) {
      latex += `\\tableofcontents
\\newpage

`;
    }

    // Chapters
    data.chapters.forEach(chapter => {
      latex += `\\chapter{${this.escapeLaTeX(chapter.title)}}
${this.markdownToLaTeX(chapter.draft)}

`;
    });

    // Appendices
    if (data.appendices.length > 0) {
      latex += `\\appendix
`;
      data.appendices.forEach(appendix => {
        latex += `\\chapter{${this.escapeLaTeX(appendix.title)}}
${this.markdownToLaTeX(appendix.content)}

`;
      });
    }

    // References
    if (data.exportOptions.includeReferences && data.citations.length > 0) {
      latex += `\\begin{thebibliography}{99}
`;
      const bibliography = citationGraphService.createBibliography(data.citations);
      bibliography.forEach((ref, index) => {
        latex += `\\bibitem{ref${index + 1}} ${this.escapeLaTeX(ref)}
`;
      });
      latex += `\\end{thebibliography}
`;
    }

    latex += `\\end{document}`;
    return latex;
  }

  /**
   * Generate DOCX content (simplified)
   */
  private generateDocxContent(data: BookExportData): string {
    // This would be similar to PDF content for now
    return this.generatePDFContent(data);
  }

  /**
   * Convert markdown to HTML (simplified)
   */
  private markdownToHTML(markdown: string): string {
    return markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/\n\n/gim, '</p><p>')
      .replace(/^(.*)$/gim, '<p>$1</p>');
  }

  /**
   * Convert markdown to LaTeX (simplified)
   */
  private markdownToLaTeX(markdown: string): string {
    return markdown
      .replace(/^# (.*$)/gim, '\\section{$1}')
      .replace(/^## (.*$)/gim, '\\subsection{$1}')
      .replace(/^### (.*$)/gim, '\\subsubsection{$1}')
      .replace(/\*\*(.*)\*\*/gim, '\\textbf{$1}')
      .replace(/\*(.*)\*/gim, '\\textit{$1}')
      .replace(/\n\n/gim, '\n\n\\par\n');
  }

  /**
   * Escape LaTeX special characters
   */
  private escapeLaTeX(text: string): string {
    return text
      .replace(/\\/g, '\\textbackslash{}')
      .replace(/[{}]/g, '\\$&')
      .replace(/[#$%&_]/g, '\\$&')
      .replace(/\^/g, '\\textasciicircum{}')
      .replace(/~/g, '\\textasciitilde{}');
  }

  /**
   * Download file to user's device
   */
  private downloadFile(blob: Blob, filename: string, mimeType: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

export default new ExportBookService();
