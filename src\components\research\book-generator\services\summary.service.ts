import { SummaryGenerationRequest, SummaryResponse } from '../types';
import { DEFAULT_BOOK_SETTINGS } from '../constants';
import bookAIService from './book-ai.service';

class SummaryService {
  /**
   * Generate a summary for a chapter
   */
  async generateChapterSummary(
    chapterContent: string,
    chapterTitle: string,
    chapterNumber: number,
    options: {
      maxWords?: number;
      extractGlossaryTerms?: boolean;
      includeKeyPoints?: boolean;
    } = {}
  ): Promise<SummaryResponse> {
    const {
      maxWords = DEFAULT_BOOK_SETTINGS.summaryMaxWords,
      extractGlossaryTerms = true,
      includeKeyPoints = true
    } = options;

    const request: SummaryGenerationRequest = {
      chapterContent,
      chapterTitle,
      chapterNumber,
      maxWords,
      extractGlossaryTerms
    };

    return await bookAIService.generateSummary(request);
  }

  /**
   * Create a manual summary (for user input)
   */
  createManualSummary(
    summary: string,
    glossaryTerms: string[] = [],
    keyPoints: string[] = []
  ): SummaryResponse {
    return {
      summary: summary.trim(),
      glossaryTerms,
      keyPoints,
      wordCount: this.countWords(summary),
      success: true
    };
  }

  /**
   * Validate a summary meets requirements
   */
  validateSummary(summary: string, maxWords: number = DEFAULT_BOOK_SETTINGS.summaryMaxWords): {
    isValid: boolean;
    issues: string[];
    wordCount: number;
  } {
    const issues: string[] = [];
    const wordCount = this.countWords(summary);

    if (!summary.trim()) {
      issues.push('Summary cannot be empty');
    }

    if (wordCount > maxWords) {
      issues.push(`Summary exceeds maximum word limit (${wordCount}/${maxWords} words)`);
    }

    if (wordCount < 50) {
      issues.push('Summary should be at least 50 words');
    }

    return {
      isValid: issues.length === 0,
      issues,
      wordCount
    };
  }

  /**
   * Extract key sentences from content for quick summary
   */
  extractKeySentences(content: string, maxSentences: number = 5): string[] {
    // Split into sentences
    const sentences = content
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 20); // Filter out very short sentences

    if (sentences.length <= maxSentences) {
      return sentences;
    }

    // Simple extraction strategy: take sentences from different parts
    const indices: number[] = [];
    
    // First sentence
    indices.push(0);
    
    // Middle sentences
    const middleCount = maxSentences - 2;
    for (let i = 1; i <= middleCount; i++) {
      const index = Math.floor((sentences.length * i) / (middleCount + 1));
      indices.push(index);
    }
    
    // Last sentence
    if (sentences.length > 1) {
      indices.push(sentences.length - 1);
    }

    return [...new Set(indices)].sort((a, b) => a - b).map(i => sentences[i]);
  }

  /**
   * Generate a quick summary from key sentences
   */
  generateQuickSummary(content: string, maxWords: number = 200): string {
    const keySentences = this.extractKeySentences(content, 5);
    let summary = keySentences.join('. ');
    
    // Truncate if too long
    const words = summary.split(/\s+/);
    if (words.length > maxWords) {
      summary = words.slice(0, maxWords).join(' ') + '...';
    }
    
    return summary;
  }

  /**
   * Extract potential glossary terms from content
   */
  extractGlossaryTerms(content: string): string[] {
    // Simple extraction based on patterns
    const terms: Set<string> = new Set();
    
    // Look for terms in quotes
    const quotedTerms = content.match(/"([^"]+)"/g);
    if (quotedTerms) {
      quotedTerms.forEach(term => {
        const cleaned = term.replace(/"/g, '').trim();
        if (cleaned.length > 2 && cleaned.length < 50) {
          terms.add(cleaned);
        }
      });
    }
    
    // Look for capitalized terms (potential proper nouns/concepts)
    const capitalizedTerms = content.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g);
    if (capitalizedTerms) {
      capitalizedTerms.forEach(term => {
        if (term.length > 3 && term.length < 30 && !this.isCommonWord(term)) {
          terms.add(term);
        }
      });
    }
    
    // Look for technical terms (words with specific patterns)
    const technicalTerms = content.match(/\b\w*(?:tion|sion|ment|ness|ity|ism|ology|graphy)\b/gi);
    if (technicalTerms) {
      technicalTerms.forEach(term => {
        if (term.length > 5 && !this.isCommonWord(term)) {
          terms.add(term.toLowerCase());
        }
      });
    }
    
    return Array.from(terms).slice(0, 15); // Limit to 15 terms
  }

  /**
   * Combine summaries from multiple chapters
   */
  combineSummaries(summaries: string[], maxWords: number = 800): string {
    const combined = summaries.join(' ');
    const words = combined.split(/\s+/);
    
    if (words.length <= maxWords) {
      return combined;
    }
    
    // Proportionally reduce each summary
    const targetWordsPerSummary = Math.floor(maxWords / summaries.length);
    
    const reducedSummaries = summaries.map(summary => {
      const summaryWords = summary.split(/\s+/);
      if (summaryWords.length <= targetWordsPerSummary) {
        return summary;
      }
      return summaryWords.slice(0, targetWordsPerSummary).join(' ') + '...';
    });
    
    return reducedSummaries.join(' ');
  }

  /**
   * Generate reading time estimate
   */
  estimateReadingTime(wordCount: number, wordsPerMinute: number = 200): number {
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Check if a word is a common word that shouldn't be in glossary
   */
  private isCommonWord(word: string): boolean {
    const commonWords = new Set([
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
      'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
      'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
      'those', 'what', 'which', 'who', 'when', 'where', 'why', 'how',
      'Chapter', 'Section', 'Figure', 'Table', 'Introduction', 'Conclusion'
    ]);
    
    return commonWords.has(word.toLowerCase());
  }
}

export default new SummaryService();
