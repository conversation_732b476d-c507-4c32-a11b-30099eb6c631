import { LucideIcon } from "lucide-react";

// Book metadata and structure types
export interface BookMeta {
  title: string;
  subtitle?: string;
  authors: string[];
  genre: string;
  keywords: string[];
  description?: string;
  targetAudience?: string;
  estimatedLength?: number; // in pages
}

export interface OutlineNode {
  id: string;
  title: string;
  level: number; // 1, 1.1, 1.1.1 etc
  numbering: string; // "1", "1.1", "1.1.1"
  description?: string;
  estimatedWords?: number;
  children?: OutlineNode[];
}

export interface ChapterState {
  id: string;
  title: string;
  chapterNumber: number;
  outline: OutlineNode[];
  draft: string; // markdown content
  summary: string | null; // 400-word summary
  glossaryTerms: string[]; // key terms for glossary
  citations: string[]; // keys into citationGraph
  figures: Figure[];
  status: 'pending' | 'generating' | 'drafted' | 'summarised' | 'locked';
  wordCount?: number;
  estimatedReadTime?: number; // in minutes
}

export interface Figure {
  id: string;
  title: string;
  caption: string;
  type: 'image' | 'chart' | 'diagram' | 'table';
  content?: string; // base64 or URL
  chapterNumber: number;
  figureNumber: number; // within chapter
  globalNumber: string; // e.g., "Fig 3-2"
}

export interface Appendix {
  id: string;
  title: string;
  content: string; // markdown
  order: number;
  type: 'glossary' | 'references' | 'index' | 'custom';
}

export interface Citation {
  id: string;
  key: string; // (Author, Year) format
  title: string;
  authors: string[];
  year: number;
  journal?: string;
  volume?: string;
  pages?: string;
  doi?: string;
  url?: string;
  type: 'article' | 'book' | 'conference' | 'website' | 'other';
  usedInChapters: number[]; // chapter numbers where cited
}

export interface BookState {
  meta: BookMeta;
  chapters: ChapterState[];
  appendices: Appendix[];
  citationGraph: Record<string, Citation>;
  globalSettings: {
    citationStyle: 'apa' | 'mla' | 'chicago' | 'ieee';
    referencesPerChapter: boolean; // vs consolidated at end
    includeIndex: boolean;
    includeGlossary: boolean;
  };
  generationSettings: {
    selectedModel: string;
    maxWordsPerChapter: number;
    styleGuide?: string;
    tone: 'academic' | 'professional' | 'casual' | 'technical';
  };
}

export interface ContextPack {
  previousFull: string; // full content of previous chapter
  priorSummaries: string[]; // summaries of all earlier chapters
  bookMeta: BookMeta;
  currentChapterNumber: number;
  totalChapters: number;
  glossaryTerms: string[]; // accumulated terms from previous chapters
}

export interface GenerationQueueItem {
  chapterId: string;
  chapterNumber: number;
  title: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  progress?: number; // 0-100
  error?: string;
}

// UI and component types
export interface ChapterCardProps {
  chapter: ChapterState;
  onEdit: (chapterId: string) => void;
  onRegenerate: (chapterId: string) => void;
  onLock: (chapterId: string) => void;
  isActive?: boolean;
}

export interface OutlineTreeProps {
  outline: OutlineNode[];
  onNodeEdit: (nodeId: string, updates: Partial<OutlineNode>) => void;
  onNodeAdd: (parentId: string | null, level: number) => void;
  onNodeDelete: (nodeId: string) => void;
  readonly?: boolean;
}

export interface SummaryBadgeProps {
  summary: string | null;
  wordCount?: number;
  glossaryTermCount?: number;
  onClick?: () => void;
}

// Export and formatting types
export interface ExportOptions {
  format: 'pdf' | 'epub' | 'latex' | 'docx';
  includeTableOfContents: boolean;
  includeFigureList: boolean;
  includeReferences: boolean;
  includeIndex: boolean;
  includeGlossary: boolean;
  chapterBreaks: boolean;
  pageNumbers: boolean;
  headerFooter: boolean;
  customStyles?: string;
}

export interface BookExportData {
  meta: BookMeta;
  chapters: ChapterState[];
  appendices: Appendix[];
  citations: Citation[];
  figures: Figure[];
  tableOfContents: OutlineNode[];
  exportOptions: ExportOptions;
}

// AI generation types
export interface ChapterGenerationRequest {
  chapterTitle: string;
  chapterNumber: number;
  outline: OutlineNode[];
  contextPack: ContextPack;
  generationSettings: BookState['generationSettings'];
  previousChapterSummary?: string;
}

export interface SummaryGenerationRequest {
  chapterContent: string;
  chapterTitle: string;
  chapterNumber: number;
  maxWords: number;
  extractGlossaryTerms: boolean;
}

// Service response types
export interface GenerationResponse {
  content: string;
  wordCount: number;
  citations: string[];
  figures?: Figure[];
  success: boolean;
  error?: string;
}

export interface SummaryResponse {
  summary: string;
  glossaryTerms: string[];
  keyPoints: string[];
  wordCount: number;
  success: boolean;
  error?: string;
}
